import { NextResponse } from 'next/server';
import { mockSmartForms } from '@/mock/data';
import { SmartForm, OdooField, SmartFormResponse } from '@/types/dashboard';
import { apiLogger } from '@/utils/logger';

// TODO: Remplacer par une vraie logique d'authentification et de récupération de données
async function getFormsForUser(userId: string | null): Promise<SmartForm[]> {
  // Si userId est null (pas d'utilisateur connecté), retourner un tableau vide ou une erreur
  if (!userId) {
    // Pour la démo, on retourne tout si pas d'ID, mais en prod, ce serait une erreur ou vide.
    // return [];
    apiLogger.warn("Aucun utilisateur identifié, retour de tous les formulaires mockés pour la démo");
  }
  // En attendant la base de données, on suppose que tous les mockSmartForms
  // appartiennent à l'utilisateur ou sont visibles par lui.
  // Plus tard, filtrer par userId ou les permissions de l'utilisateur.
  apiLogger.debug("Contenu de mockSmartForms avant retour", { formsCount: mockSmartForms.length });
  return mockSmartForms;
}

export async function GET(request: Request) {
  // TODO: Récupérer l'ID de l'utilisateur authentifié (par exemple, via NextAuth.js session)
  const userId = null; // Simuler un utilisateur non identifié ou récupérer le vrai ID
  apiLogger.info("Requête GET reçue sur /api/smart-forms");

  try {
    const forms = await getFormsForUser(userId);
    apiLogger.info("Données renvoyées au client", { formsCount: forms.length });
    return NextResponse.json(forms);
  } catch (error) {
    apiLogger.error("Erreur lors de la récupération des formulaires", error);
    return new NextResponse('Erreur interne du serveur', { status: 500 });
  }
}

interface NewSmartFormPayload {
  id?: string; // ID optionnel pour les formulaires wizard
  title: string;
  description?: string;
  // On s'attend à ce que les champs soient déjà dans le bon format OdooField[] ou adaptable
  fields: OdooField[];
  // Ajouter d'autres champs de SmartFormConfig si nécessaire pour la création (ex: recipients, deadline)
  // Pour la démo, on se concentre sur le minimum pour l'affichage.
}

export async function POST(request: Request) {
  console.log("API POST /api/smart-forms: Requête de création reçue.");
  try {
    const payload = await request.json() as NewSmartFormPayload;
    console.log("API POST /api/smart-forms: Payload reçu:", payload);

    if (!payload.title || !payload.fields || payload.fields.length === 0) {
      return new NextResponse('Données de formulaire invalides: titre et champs sont requis.', { status: 400 });
    }

    // Utiliser l'ID fourni ou en générer un nouveau
    const newFormId = payload.id || `sf-${Date.now()}`;
    const now = new Date();

    const newSmartForm: SmartForm = {
      id: newFormId,
      name: payload.title,
      description: payload.description || '',
      fields: payload.fields, // Doit être OdooField[]
      status: 'sent', // Ou 'draft' si un processus de publication est prévu
      createdAt: now,
      updatedAt: now,
      responses: [],
      template: false, // Par défaut, un formulaire créé n'est pas un template
      // recipients: payload.recipients, // à ajouter si géré
      // deadline: payload.deadline, // à ajouter si géré
    };

    mockSmartForms.unshift(newSmartForm); // Ajoute au début de la liste pour une visibilité facile

    console.log(`API POST /api/smart-forms: Nouveau formulaire ${newFormId} ajouté à mockSmartForms.`);
    console.log("API POST /api/smart-forms: mockSmartForms après ajout:", JSON.stringify(mockSmartForms, null, 2));

    return NextResponse.json(newSmartForm, { status: 201 }); // 201 Created

  } catch (error) {
    console.error("API POST /api/smart-forms: Erreur lors de la création du formulaire:", error);
    if (error instanceof SyntaxError) {
        return new NextResponse('Payload JSON invalide', { status: 400 });
    }
    return new NextResponse('Erreur interne du serveur lors de la création du formulaire', { status: 500 });
  }
}

// TODO: Implémenter la fonction POST pour créer de nouveaux SmartForms
// export async function POST(request: Request) { ... } 