'use client';

import React, { useState, useMemo, useEffect, useRef } from 'react';
import { TextField, Popover, Button, Flex, Text, ScrollArea, Box } from '@radix-ui/themes';
import { ChevronDownIcon, CheckIcon, MagnifyingGlassIcon } from '@radix-ui/react-icons';

export interface SearchableSelectOption {
  value: string;
  label: string;
}

interface SearchableSelectProps {
  options: SearchableSelectOption[];
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  label?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  size?: '1' | '2' | '3';
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Sélectionner...",
  label,
  disabled,
  required,
  className,
  size = '3' // Corresponds à la taille par défaut des TextField/Select Radix
}) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [popoverWidth, setPopoverWidth] = useState<number | undefined>(undefined);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open && triggerRef.current) {
      setPopoverWidth(triggerRef.current.offsetWidth);
    }
  }, [open]);

  useEffect(() => {
    if (open && inputRef.current) {
      // Focus sur le champ de recherche quand le popover s'ouvre
      setTimeout(() => inputRef.current?.focus(), 0);
    }
    if (!open) {
      setSearchTerm(''); // Réinitialiser la recherche à la fermeture
    }
  }, [open]);

  const filteredOptions = useMemo(() => {
    if (!searchTerm) return options;
    return options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [options, searchTerm]);

  const selectedOptionDetails = options.find(opt => opt.value === value);

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setOpen(false);
  };

  return (
    <Box className={className}>
      {label && (
         <Text as="label" size="2" weight="medium" mb="1" style={{ display: 'block' }}>
           {label} {required && <Text color="red">*</Text>}
         </Text>
      )}
      <Popover.Root open={open} onOpenChange={setOpen}>
        <Popover.Trigger>
          <Button 
            ref={triggerRef} 
            variant="surface" 
            color="gray" 
            style={{ width: '100%', justifyContent: 'space-between', textAlign: 'left' }} 
            disabled={disabled}
            size={size}
          >
            <Text truncate style={{ color: selectedOptionDetails ? 'var(--gray-12)' : 'var(--gray-a9)' }}>
              {selectedOptionDetails ? selectedOptionDetails.label : placeholder}
            </Text>
            <ChevronDownIcon color="gray" />
          </Button>
        </Popover.Trigger>
        <Popover.Content 
          style={{ 
            width: popoverWidth ? `${popoverWidth}px` : 'var(--radix-popover-trigger-width)', 
            minWidth: '200px',
            padding: '0',
            overflow: 'hidden'
          }}
          sideOffset={5}
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <Box p="2">
            <TextField.Root
              ref={inputRef}
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size={size === '3' ? '2' : '1'}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && filteredOptions.length === 1) {
                  handleSelect(filteredOptions[0].value);
                  e.preventDefault();
                }
                if (e.key === 'Escape') {
                  setOpen(false);
                }
              }}
            >
              <TextField.Slot>
                <MagnifyingGlassIcon height="16" width="16" />
              </TextField.Slot>
            </TextField.Root>
          </Box>
          <ScrollArea style={{ maxHeight: '200px', overflowX: 'hidden' }}>
            <Box style={{ overflow: 'hidden' }}>
              <Flex direction="column" gap="1" p="1" pt="0">
                {filteredOptions.length > 0 ? (
                  filteredOptions.map(option => (
                    <Button
                      key={option.value}
                      variant={option.value === value ? "soft" : "ghost"}
                      color={option.value === value ? "blue" : "gray"}
                      onClick={() => handleSelect(option.value)}
                      style={{ 
                        justifyContent: 'space-between', 
                        width: '100%', 
                        textAlign: 'left',
                        overflow: 'hidden'
                      }}
                      size={size === '3' ? '2' : '1'}
                    >
                      <Text truncate size={size === '3' ? '2' : '1'}>{option.label}</Text>
                      {option.value === value && <CheckIcon />}
                    </Button>
                  ))
                ) : (
                  <Box p="3" style={{ textAlign: 'center' }}>
                    <Text size="2" color="gray">Aucun résultat</Text>
                  </Box>
                )}
              </Flex>
            </Box>
          </ScrollArea>
        </Popover.Content>
      </Popover.Root>
    </Box>
  );
};

export default SearchableSelect; 