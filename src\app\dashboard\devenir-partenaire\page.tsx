'use client';

import React, { useState } from 'react';
import { <PERSON>, Flex, Card, Heading, Text, Button, TextField, TextArea, Callout } from '@radix-ui/themes';
import { CheckCircledIcon } from '@radix-ui/react-icons';
import Link from 'next/link';

interface FormData {
  companyName: string;
  contactName: string;
  contactEmail: string;
  website: string;
  description: string;
  integrationInterest: string;
}

export default function BecomePartnerPage() {
  const [formData, setFormData] = useState<FormData>({
    companyName: '',
    contactName: '',
    contactEmail: '',
    website: '',
    description: '',
    integrationInterest: '',
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: Partial<FormData> = {};
    if (!formData.companyName.trim()) newErrors.companyName = "Le nom de l'entreprise est requis.";
    if (!formData.contactName.trim()) newErrors.contactName = "Le nom du contact est requis.";
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = "L'email de contact est requis.";
    } else if (!/^\S+@\S+\.\S+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = "L'adresse email n'est pas valide.";
    }
    if (!formData.website.trim()) {
        newErrors.website = "Le site web est requis.";
    } else if (!/^https?:\/\/.+/.test(formData.website)) {
        newErrors.website = "L'URL du site web doit commencer par http:// ou https://";
    }
    if (!formData.description.trim()) newErrors.description = "Une description de votre solution est requise.";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      console.log('Formulaire soumis:', formData);
      setIsSubmitted(true);
    }
  };

  return (
    <Box style={{ maxWidth: 'var(--layout-max-width, 800px)', margin: '0 auto' }} p={{ initial: '3', md: '6' }}>
      <Heading as="h1" size="8" weight="bold" mb="3" align="center">
        Devenez Partenaire Diktasis
      </Heading>
      <Text as="p" size="4" color="gray" align="center" mb="7">
        Intégrez votre solution à Diktasis et offrez une expérience connectée à des milliers d'utilisateurs.
        En devenant partenaire, votre application apparaîtra dans notre catalogue, permettant une activation simple et rapide
        pour une synchronisation de données fluide et native.
      </Text>

      {isSubmitted ? (
        <Callout.Root color="green" role="alert" mb="6">
          <Callout.Icon>
            <CheckCircledIcon />
          </Callout.Icon>
          <Callout.Text>
            Merci pour votre demande ! Nous avons bien reçu vos informations et reviendrons vers vous prochainement.
          </Callout.Text>
        </Callout.Root>
      ) : (
        <Card size="4" mb="6">
          <form onSubmit={handleSubmit}>
            <Flex direction="column" gap="5">
              <Heading as="h2" size="6" weight="medium" mb="1">
                Informations sur votre entreprise
              </Heading>
              
              <div>
                <TextField.Root 
                  name="companyName"
                  placeholder="Nom de votre entreprise" 
                  value={formData.companyName}
                  onChange={handleChange}
                  size="3"
                  style={{width: '100%'}}
                />
                {errors.companyName && <Text size="1" color="red" mt="1" as="p">{errors.companyName}</Text>}
              </div>

              <div>
                <TextField.Root 
                  name="contactName"
                  placeholder="Nom du contact principal" 
                  value={formData.contactName}
                  onChange={handleChange}
                  size="3"
                  style={{width: '100%'}}
                />
                {errors.contactName && <Text size="1" color="red" mt="1" as="p">{errors.contactName}</Text>}
              </div>

              <div>
                <TextField.Root 
                  type="email"
                  name="contactEmail"
                  placeholder="Email de contact" 
                  value={formData.contactEmail}
                  onChange={handleChange}
                  size="3"
                  style={{width: '100%'}}
                />
                {errors.contactEmail && <Text size="1" color="red" mt="1" as="p">{errors.contactEmail}</Text>}
              </div>

              <div>
                <TextField.Root 
                  name="website"
                  type="url"
                  placeholder="Lien vers votre site web (ex: https://example.com)" 
                  value={formData.website}
                  onChange={handleChange}
                  size="3"
                  style={{width: '100%'}}
                />
                {errors.website && <Text size="1" color="red" mt="1" as="p">{errors.website}</Text>}
              </div>

              <Heading as="h3" size="5" weight="medium" mt="3" mb="1">
                À propos de votre solution
              </Heading>
              <div>
                <TextArea 
                  name="description"
                  placeholder="Décrivez brièvement votre application ou service." 
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  size="3"
                  style={{width: '100%'}}
                />
                {errors.description && <Text size="1" color="red" mt="1" as="p">{errors.description}</Text>}
              </div>
              
              <div>
                <TextArea 
                  name="integrationInterest"
                  placeholder="Pourquoi souhaitez-vous vous intégrer à Diktasis ? Quels cas d'usage voyez-vous ? (Optionnel)" 
                  value={formData.integrationInterest}
                  onChange={handleChange}
                  rows={4}
                  size="3"
                  style={{width: '100%'}}
                />
              </div>
              
              <Flex justify="end" mt="4">
                <Button type="submit" size="3" variant="solid" color="blue" highContrast style={{ cursor: 'pointer' }}>
                  Soumettre la demande
                </Button>
              </Flex>
            </Flex>
          </form>
        </Card>
      )}
      <Flex justify="center" mt="6">
          <Link href="/dashboard/data-sources" passHref>
            <Button asChild variant="soft" color="gray">
              <a style={{ cursor: 'pointer' }}>Retour au catalogue des sources de données</a>
            </Button>
          </Link>
        </Flex>
    </Box>
  );
} 