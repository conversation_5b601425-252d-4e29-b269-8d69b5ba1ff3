.rdpCustom {
  --rdp-accent-color: var(--accent-9);
  --rdp-background-color: var(--color-panel-translucent);
  --rdp-accent-color-dark: var(--accent-9);
  --rdp-background-color-dark: var(--color-panel-translucent);
  --rdp-cell-size: 40px;
  --rdp-caption-font-weight: bold;
  --rdp-caption-font-size: var(--font-size-3);
  --rdp-nav-button-height: var(--rdp-cell-size);
  --rdp-nav-button-width: var(--rdp-cell-size);
  font-size: var(--font-size-2);
  margin: 0;
}

.rdpCustom .rdp-months {
  display: flex;
  justify-content: center;
}

.rdpCustom .rdp-month {
  margin: 0;
}

.rdpCustom .rdp-table {
  border-collapse: collapse;
  width: 100%;
}

.rdpCustom .rdp-head_cell {
  font-weight: 500;
  color: var(--gray-11);
  font-size: 12px;
  text-align: center;
  padding: 8px 0;
}

.rdpCustom .rdp-cell {
  text-align: center;
  padding: 2px;
}

.rdpCustom .rdp-day_button {
  border: none;
  background: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--gray-12);
  font-size: 14px;
  transition: all 0.1s ease;
}

.rdpCustom .rdp-day_button:hover {
  background-color: var(--gray-3);
}

.rdpCustom .rdp-day_button:focus {
  outline: 2px solid var(--accent-7);
  outline-offset: 2px;
}

.rdpCustom .rdp-day_button[data-selected="true"] {
  background-color: var(--accent-9);
  color: white;
  font-weight: 500;
}

.rdpCustom .rdp-day_button[data-today="true"] {
  font-weight: 600 !important;
  color: var(--accent-9) !important;
  background-color: var(--accent-3) !important;
  border: 2px solid var(--accent-6) !important;
  box-sizing: border-box !important;
}

.rdpCustom .rdp-day_button[data-outside="true"] {
  color: var(--gray-8);
}

.rdpCustom .rdp-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.rdpCustom .rdp-nav_button {
  border: none;
  background: none;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius-2);
  color: var(--gray-11);
  transition: all 0.1s ease;
}

.rdpCustom .rdp-nav_button:hover {
  background-color: var(--gray-3);
  color: var(--gray-12);
}

.rdpCustom .rdp-caption_label {
  font-weight: 600;
  color: var(--gray-12);
  font-size: 16px;
}

/* Classe personnalisée pour la date du jour */
.todayHighlight {
  font-weight: bold;
  color: var(--accent-11) !important; /* Assurer la visibilité du texte */
  background-color: var(--accent-a3) !important;
  border: 1px solid var(--accent-7) !important;
  border-radius: var(--radius-2);
}

.todayHighlight:is([aria-selected="true"], .rdp-day_selected) {
  background-color: var(--accent-9) !important;
  color: white !important;
  border: 1px solid var(--accent-9) !important;
}

/* Style pour les jours de week-end */
.weekendDay {
  color: var(--gray-a9) !important; /* Couleur de texte grise douce pour le week-end */
  /* Pas de changement de fond pour ne pas suggérer une interdiction */
}

/* Si un jour de week-end est aussi "aujourd'hui" */
.weekendDay.todayHighlight {
  color: var(--accent-11) !important; /* Le style de todayHighlight pour le texte prévaut */
  background-color: var(--accent-a3) !important; /* Fond de todayHighlight */
  border: 1px solid var(--accent-7) !important; /* Bordure de todayHighlight */
}

/* Si un jour de week-end est sélectionné */
.weekendDay:is([aria-selected="true"], .rdp-day_selected):not(.todayHighlight) {
  background-color: var(--accent-9) !important; /* Fond de sélection normal */
  color: white !important; /* Texte blanc sur fond sélectionné */
}

.pastDay {
  color: var(--gray-a8) !important;
  background-color: var(--gray-a2) !important;
  opacity: 0.6 !important;
  text-decoration: line-through !important; /* Barrer pour indiquer non disponible */
  cursor: not-allowed !important;
}

/* S'assurer que les jours passés sélectionnés ou "aujourd'hui" (ne devrait pas arriver) gardent ce style */
.pastDay:is([aria-selected="true"], .rdp-day_selected),
.pastDay.todayHighlight {
  color: var(--gray-a8) !important;
  background-color: var(--gray-a2) !important;
  opacity: 0.6 !important;
  text-decoration: line-through !important;
}

/* Style spécial quand la date du jour est aussi sélectionnée */
.rdpCustom .rdp-day_button[data-today="true"][data-selected="true"],
.todayHighlight[data-selected="true"] {
  background-color: var(--accent-9) !important;
  color: white !important;
  border: 2px solid var(--accent-11) !important;
  font-weight: 600 !important;
} 