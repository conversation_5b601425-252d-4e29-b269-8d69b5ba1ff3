'use client';

import React, { useState, useEffect } from 'react';
import { DayPicker } from 'react-day-picker';
import { format, parse, isValid, addDays, subDays, startOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { 
  Popover, 
  Button, 
  TextField, 
  Box,
  Flex,
  IconButton
} from '@radix-ui/themes';
import { CalendarIcon, ArrowUpIcon, ArrowDownIcon } from '@radix-ui/react-icons';
import 'react-day-picker/dist/style.css';
import styles from './DatePicker.module.css';

interface DatePickerProps {
  value?: string; // Format ISO (YYYY-MM-DD)
  onChange?: (value: string) => void;
  placeholder?: string;
  size?: "1" | "2" | "3";
  disabled?: boolean;
}

const DATE_FORMAT_DISPLAY = 'dd/MM/yyyy';
const DATE_FORMAT_DISPLAY_WITH_DAY = 'dd/MM/yyyy (EEE)';
const PLACEHOLDER_TEXT = "jj/mm/aaaa";

export default function DatePicker({ 
  value, 
  onChange, 
  placeholder = "jj/mm/aaaa",
  size = "3",
  disabled = false 
}: DatePickerProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(() => {
    if (value) {
      const date = new Date(value + 'T00:00:00'); // Assurer l'interprétation en UTC
      return isValid(date) ? format(date, DATE_FORMAT_DISPLAY_WITH_DAY, { locale: fr }) : PLACEHOLDER_TEXT;
    }
    return PLACEHOLDER_TEXT;
  });

  useEffect(() => {
    if (value) {
      const date = new Date(value + 'T00:00:00');
      if (isValid(date)) {
        setInputValue(format(date, DATE_FORMAT_DISPLAY_WITH_DAY, { locale: fr }));
      } else {
        setInputValue(PLACEHOLDER_TEXT);
      }
    } else {
      setInputValue(PLACEHOLDER_TEXT);
    }
  }, [value]);

  const selectedDate = value ? new Date(value + 'T00:00:00') : undefined;

  const handleDayAdjustment = (days: number) => {
    const currentDate = value ? new Date(value + 'T00:00:00') : new Date();
    const newDate = days > 0 ? addDays(currentDate, days) : subDays(currentDate, Math.abs(days));
    const isoDate = format(newDate, 'yyyy-MM-dd');
    onChange?.(isoDate);
    // inputValue sera mis à jour via useEffect
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Essayer de parser la date, même avec le jour entre parenthèses
    const datePart = newValue.split(' (')[0]; // Garde "jj/mm/aaaa"
    const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = datePart.match(dateRegex);
    
    if (match) {
      try {
        const parsedDate = parse(datePart, DATE_FORMAT_DISPLAY, new Date());
        if (isValid(parsedDate)) {
          const isoDate = format(parsedDate, 'yyyy-MM-dd');
          onChange?.(isoDate);
        } else {
          onChange?.(''); // Si la date n'est pas valide après parsing
        }
      } catch (error) {
        onChange?.('');
      }
    } else if (newValue === '' || newValue === PLACEHOLDER_TEXT) {
      onChange?.('');
    }
  };

  const handleInputFocus = () => {
    if (inputValue === PLACEHOLDER_TEXT) {
      setInputValue('');
    }
  };

  const handleInputBlur = () => {
    if (inputValue === '') {
      setInputValue(PLACEHOLDER_TEXT);
    } else {
      // Revalider/reformater au blur si une date valide est là, sinon remettre le placeholder
      if (value) {
        const date = new Date(value + 'T00:00:00');
        if (isValid(date)) {
          setInputValue(format(date, DATE_FORMAT_DISPLAY_WITH_DAY, { locale: fr }));
        }
      } else if (!value && inputValue !== PLACEHOLDER_TEXT) {
         // Si l'utilisateur a tapé qqch qui n'a pas resulté en un `value` valide
         // et que ce n'est pas déjà le placeholder, on le remet.
         const datePart = inputValue.split(' (')[0];
         const parsed = parse(datePart, DATE_FORMAT_DISPLAY, new Date());
         if (!isValid(parsed)){
            setInputValue(PLACEHOLDER_TEXT);
         }
      }
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      const isoDate = format(date, 'yyyy-MM-dd');
      onChange?.(isoDate);
      // inputValue sera mis à jour via useEffect
      setOpen(false);
    }
  };

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Flex direction="column" gap="2">
        <Flex align="center" gap="1"> {/* Réduction du gap pour les slots */} 
          <TextField.Root
            value={inputValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder={placeholder} // Le placeholder de la prop est gardé, mais l'input est géré par PLACEHOLDER_TEXT
            size={size}
            disabled={disabled}
            style={{
              flex: 1,
              color: inputValue === PLACEHOLDER_TEXT ? 'var(--gray-9)' : 'var(--gray-12)'
            }}
          >
            <TextField.Slot side="left">
              <Flex align="center" gap="2">
                <IconButton size="2" variant="ghost" onClick={() => handleDayAdjustment(-1)} disabled={disabled} aria-label="Diminuer d'un jour">
                  <ArrowDownIcon />
                </IconButton>
                <IconButton size="2" variant="ghost" onClick={() => handleDayAdjustment(1)} disabled={disabled} aria-label="Augmenter d'un jour">
                  <ArrowUpIcon />
                </IconButton>
              </Flex>
            </TextField.Slot>
          </TextField.Root>
          <Popover.Trigger>
            <Button
              variant="soft"
              size={size}
              disabled={disabled}
              style={{
                minWidth: 'auto',
                whiteSpace: 'nowrap'
              }}
            >
              <CalendarIcon width={16} height={16} />
              Choisir une date
            </Button>
          </Popover.Trigger>
        </Flex>
      </Flex>
      
      <Popover.Content 
        align="start" 
        side="bottom"
        style={{ 
          padding: '16px',
          backgroundColor: 'var(--color-panel-solid)',
          border: '1px solid var(--gray-6)',
          borderRadius: 'var(--radius-3)',
          boxShadow: 'var(--shadow-5)',
          width: '320px'
        }}
      >
        <DayPicker
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          locale={fr}
          showOutsideDays
          className={styles.rdpCustom}
          modifiers={{
            today: new Date(),
            weekend: (date) => date.getDay() === 0 || date.getDay() === 6,
            past: (date) => date < startOfDay(new Date())
          }}
          modifiersClassNames={{
            today: styles.todayHighlight,
            weekend: styles.weekendDay,
            past: styles.pastDay
          }}
          disabled={{ before: startOfDay(new Date()) }}
        />
      </Popover.Content>
    </Popover.Root>
  );
} 