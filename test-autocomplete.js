// Test file pour vérifier l'auto-complétion
// Ta<PERSON>z le code suivant et regardez les suggestions :

function calculateSum(a, b) {
  // Ta<PERSON>z "return a + " et regardez les suggestions
}

// Tapez "const users = " et regardez les suggestions

// Tapez "fetch(" et regardez les suggestions

// Si vous voyez des suggestions grises automatiques = Copilot actif
// Si vous voyez seulement des suggestions dans une popup = Copilot désactivé
