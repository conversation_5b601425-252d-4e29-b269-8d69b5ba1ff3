"use client";
import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from 'firebase/auth';
import { observeUser } from '@/firebaseConfig';
import { authLogger } from '@/utils/logger';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  // Ajoutez ici d'autres champs si nécessaire, par exemple :
  // organization: string | null; 
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  // const [organization, setOrganization] = useState<string | null>(null);

  useEffect(() => {
    authLogger.debug('Setting up auth observer');

    // Timeout de sécurité pour éviter le chargement infini
    const timeout = setTimeout(() => {
      authLogger.warn('Auth timeout reached, stopping loading');
      setLoading(false);
    }, 5000);

    const unsubscribe = observeUser((firebaseUser) => {
      authLogger.info('User state changed', { userId: firebaseUser?.uid || 'no user' });
      clearTimeout(timeout);
      setUser(firebaseUser);
      setLoading(false);
      // Si vous avez besoin de récupérer l'organisation ou d'autres infos spécifiques
      // après la connexion, vous pouvez le faire ici.
      // Par exemple, appeler une fonction qui récupère les infos de l'organisation
      // depuis votre backend ou Firestore, en utilisant firebaseUser.uid
      // if (firebaseUser) {
      //   fetchOrganization(firebaseUser.uid).then(setOrganization);
      // } else {
      //   setOrganization(null);
      // }
    });

    return () => {
      clearTimeout(timeout);
      unsubscribe();
    };
  }, []);

  return (
    <AuthContext.Provider value={{ user, loading /*, organization */ }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 