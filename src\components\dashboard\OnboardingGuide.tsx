import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, Flex, Grid, Heading, Text } from '@radix-ui/themes';
import { useRouter } from 'next/navigation';

export const OnboardingGuide: React.FC = () => {
  const router = useRouter();

  return (
    <Card size="3" style={{ 
      background: 'linear-gradient(135deg, var(--blue-2), var(--purple-2))',
      border: '1px solid var(--blue-6)'
    }}>
      <Flex direction="column" gap="4">
        <Heading as="h2" size="5" weight="bold" color="blue">
          🚀 Découvrez Diktasis en 3 étapes
        </Heading>
        <Text size="3" color="gray">
          Automatisez la collecte de données clients et synchronisez avec vos outils existants
        </Text>
        <Grid columns={{ initial: "1", md: "3" }} gap="4">
          <Box>
            <Text weight="bold" size="3" mb="2" as="div">1. Connectez vos sources</Text>
            <Text size="2" color="gray" mb="3" as="div">
              <PERSON>z votre CRM, ERP ou tableurs pour centraliser vos données
            </Text>
            <Button 
              size="2" 
              variant="soft" 
              color="blue"
              onClick={() => router.push('/dashboard/data-sources')}
            >
              Voir les sources →
            </Button>
          </Box>
          <Box>
            <Text weight="bold" size="3" mb="2" as="div">2. Créez un SmartForm</Text>
            <Text size="2" color="gray" mb="3" as="div">
              Collectez des données intelligemment avec des formulaires adaptatifs
            </Text>
            <Button 
              size="2" 
              variant="soft" 
              color="purple"
              onClick={() => router.push('/dashboard/new-smart-forms')}
            >
              Créer maintenant →
            </Button>
          </Box>
          <Box>
            <Text weight="bold" size="3" mb="2" as="div">3. Automatisez</Text>
            <Text size="2" color="gray" mb="3" as="div">
              Synchronisez automatiquement les données avec vos outils
            </Text>
            <Button 
              size="2" 
              variant="soft" 
              color="green"
              onClick={() => router.push('/dashboard/smartforms')}
            >
              Voir la démo →
            </Button>
          </Box>
        </Grid>
      </Flex>
    </Card>
  );
}; 