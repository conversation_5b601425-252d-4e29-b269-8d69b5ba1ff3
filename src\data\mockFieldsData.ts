import { FieldToCollect } from './mockWizardData';

// Mock des champs par source (version optimisée et réduite)
export const mockFieldsBySource: { [sourceId: string]: FieldToCollect[] } = {
  quickbooks: [
    {
      id: 'qb_company_name',
      name: "Nom de l'entreprise",
      description: 'Raison sociale pour la facturation',
      sourceId: 'quickbooks',
      sourceName: 'QuickBooks Online',
      required: true,
      type: 'text',
      currentValue: 'QuantumLeap Inc.',
      completionStatus: 'complete'
    },
    {
      id: 'qb_contact_email',
      name: 'Email contact',
      description: 'Email du contact principal',
      sourceId: 'quickbooks',
      sourceName: 'QuickBooks Online',
      required: false,
      type: 'email',
      currentValue: '<EMAIL>',
      completionStatus: 'complete'
    },
    {
      id: 'qb_tax_id',
      name: 'Numéro de TVA',
      description: 'Numéro de TVA intracommunautaire',
      sourceId: 'quickbooks',
      sourceName: 'QuickBooks Online',
      required: true,
      type: 'text',
      completionStatus: 'empty'
    },
    {
      id: 'qb_billing_address',
      name: 'Adresse de facturation',
      description: 'Adresse complète pour les factures',
      sourceId: 'quickbooks',
      sourceName: 'QuickBooks Online',
      required: true,
      type: 'textarea',
      currentValue: '123 Tech Valley, Silicon City',
      completionStatus: 'partial'
    },
    {
      id: 'qb_payment_terms',
      name: 'Conditions de paiement',
      description: 'Délai de règlement standard',
      sourceId: 'quickbooks',
      sourceName: 'QuickBooks Online',
      required: false,
      type: 'select',
      options: ['30 jours', '45 jours', '60 jours', 'Comptant'],
      currentValue: '30 jours',
      completionStatus: 'complete'
    }
  ],
  odoo: [
    {
      id: 'odoo_company_name',
      name: "Nom de l'entreprise",
      description: 'Raison sociale officielle',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: true,
      type: 'text',
      currentValue: 'Onyx Trading Co. Ltd',
      completionStatus: 'complete'
    },
    {
      id: 'odoo_contact_email',
      name: 'Email contact principal',
      description: 'Email du contact principal',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: true,
      type: 'email',
      currentValue: '<EMAIL>',
      completionStatus: 'complete'
    },
    {
      id: 'odoo_phone',
      name: 'Téléphone',
      description: 'Numéro de téléphone principal',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: false,
      type: 'text',
      currentValue: '+33 1 23 45 67 89',
      completionStatus: 'complete'
    },
    {
      id: 'odoo_website',
      name: 'Site web',
      description: 'URL du site web de l\'entreprise',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: false,
      type: 'text',
      currentValue: 'https://onyxtrading.com',
      completionStatus: 'complete'
    },
    {
      id: 'odoo_industry',
      name: 'Secteur d\'activité',
      description: 'Domaine d\'activité principal',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: false,
      type: 'select',
      options: ['Trading', 'Technology', 'Services', 'Manufacturing', 'Retail'],
      currentValue: 'Trading',
      completionStatus: 'complete'
    },
    {
      id: 'odoo_employee_count',
      name: 'Nombre d\'employés',
      description: 'Effectif de l\'entreprise',
      sourceId: 'odoo',
      sourceName: 'Odoo',
      required: false,
      type: 'select',
      options: ['1-10', '11-50', '51-200', '201-500', '500+'],
      currentValue: '11-50',
      completionStatus: 'complete'
    }
  ]
};
