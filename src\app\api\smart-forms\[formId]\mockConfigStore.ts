// Base de données fictive des configurations de formulaires qui ne sont pas dans mockSmartForms (ex: démos pures)
// Utilisée comme fallback par la fonction GET de src/app/api/smart-forms/[formId]/route.ts
export const mockFormConfigs: { [key: string]: any } = {
    'form-demo-1': {
      formId: 'form-demo-1',
      title: 'Formulaire de Démonstration N°1',
      description: 'Ceci est le premier formulaire de démonstration. Merci de le remplir attentivement.',
      fields: [
        { id: 'nom_prenom', name: 'nom_prenom', label: 'Nom et Prénom', type: 'text', required: true, placeholder: 'ex: <PERSON>', example: 'Votre nom complet' },
        { id: 'email_contact', name: 'email_contact', label: 'Votre Email', type: 'email', required: true, placeholder: 'ex: <EMAIL>', example: 'Adresse email valide' },
        {
          id: 'siret_number',
          name: 'Numéro SIRET',
          label: 'Numéro SIRET',
          type: 'text',
          required: false,
          placeholder: '123 456 789 00012',
          example: '14 chiffres'
        },
        { id: 'satisfaction', name: 'satisfaction', label: 'Êtes-vous satisfait du service ?', type: 'select', options: ['Oui', 'Non', 'Peut-être'], required: true, placeholder: 'Choisissez une option' },
      ],
    },
    'form-demo-2': {
      formId: 'form-demo-2',
      title: 'Formulaire de Feedback Projet',
      description: 'Donnez-nous votre avis sur le projet Alpha.',
      fields: [
        { id: 'nom_projet', name: 'nom_projet', label: 'Nom du Projet', type: 'text', required: true, value: 'Projet Alpha', readOnly: true },
        { id: 'commentaire_general', name: 'commentaire_general', label: 'Commentaire général', type: 'textarea', required: true, rows: 5 },
        { id: 'note_globale', name: 'note_globale', label: 'Note globale (sur 5)', type: 'number', required: true, min: 1, max: 5 },
        { id: 'recommander', name: 'recommander', label: 'Recommanderiez-vous ce projet ?', type: 'checkbox', required: false },
      ],
    },
    'default-form': {
      formId: 'default-form',
      title: 'Formulaire de Contact Standard',
      description: 'Merci de remplir les informations demandées ci-dessous.',
      fields: [
          { id: 'nom_complet', name: 'nom_complet', label: 'Nom complet', type: 'text', required: true },
          { id: 'email', name: 'email', label: 'Adresse e-mail', type: 'email', required: true },
          { id: 'entreprise', name: 'entreprise', label: 'Nom de l\'entreprise', type: 'text', required: false },
          { id: 'poste', name: 'poste', label: 'Poste occupé', type: 'text', required: false },
          { id: 'feedback', name: 'feedback', label: 'Votre feedback', type: 'textarea', required: true },
          { 
              id: 'service_interesse', 
              name: 'service_interesse', 
              label: 'Quel service vous intéresse ?', 
              type: 'select', 
              required: true,
              options: ['Service A', 'Service B', 'Service C']
          },
          { id: 'consent_rgpd', name: 'consent_rgpd', label: 'J\'accepte les termes et conditions', type: 'checkbox', required: true },
      ],
    }
  }; 