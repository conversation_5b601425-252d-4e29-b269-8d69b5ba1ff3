'use client';
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { Theme, Button, Avatar, Box, Flex, Text, Separator } from '@radix-ui/themes';
import {
  DashboardIcon,
  FileTextIcon,
  PlusCircledIcon,
  PersonIcon,
  GearIcon,
  ExitIcon,
  LayersIcon,
  MagicWandIcon,
} from '@radix-ui/react-icons';
import { logout } from '@/firebaseConfig';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const navLinks = [
  { name: 'Dashboard', href: '/dashboard', icon: DashboardIcon },
  { name: 'Mes SmartForms', href: '/dashboard/smart-forms', icon: FileTextIcon },
  { name: '<PERSON><PERSON>', href: '/dashboard/templates', icon: MagicWandIcon },
  { name: 'Nouveau SmartForm', href: '/dashboard/new-smart-forms', icon: PlusCircledIcon },
  { name: 'Data Sources', href: '/dashboard/data-sources', icon: LayersIcon },
  { name: 'Team', href: '/dashboard/team', icon: PersonIcon },
  { name: 'Settings', href: '/dashboard/settings', icon: GearIcon },
];

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
    }
  };

  if (loading) {
    return <Flex align="center" justify="center" style={{ minHeight: '100vh' }}><Text>Chargement du dashboard...</Text></Flex>;
  }

  const displayName = user?.displayName || user?.email || 'Utilisateur';
  const userEmail = user?.email || 'Non connecté';
  const avatarFallback = displayName ? displayName[0].toUpperCase() : 'U';
  const photoURL = user?.photoURL;

  return (
    <Theme appearance="light" accentColor="blue" grayColor="slate" panelBackground="solid" scaling="100%">
      <Flex direction="row" style={{ minHeight: '100vh' }}>
        <Box
          style={{
            width: 260,
            backgroundColor: 'var(--slate-2)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            borderRight: '1px solid var(--slate-6)'
          }}
          p="4"
        >
          <Flex direction="column" gap="4" style={{ flexGrow: 1}}>
            <Flex align="center" gap="3" mb="4">
              <svg width="32" height="32" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="20" fill="var(--blue-9)" />
                <path d="M13 27L20 13L27 27" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                <circle cx="20" cy="24" r="2" fill="white" />
            </svg>
              <Text size="5" weight="bold" style={{ color: 'var(--gray-12)' }}>Diktasis</Text>
            </Flex>

            <Flex direction="column" gap="1" asChild>
              <nav>
                {navLinks.map((link) => {
                  const Icon = link.icon;
                  const isActive = pathname === link.href;
                  return (
                    <Button
                key={link.name}
                      asChild
                      size="3"
                      variant={isActive ? 'soft' : 'ghost'}
                      color={isActive ? 'blue' : 'gray'}
                      highContrast={isActive}
                      style={{
                        justifyContent: 'flex-start',
                        height: 48,
                      }}
                    >
                      <Link href={link.href} style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-3)', width: '100%' }}>
                        <Icon width={20} height={20} />
                        <Text weight={isActive ? "bold" : "regular"} size="3">{link.name}</Text>
              </Link>
                    </Button>
                  );
                })}
          </nav>
            </Flex>
          </Flex>

          <Box>
            <Separator my="3" size="4" style={{ backgroundColor: 'var(--slate-6)' }}/>
            {user && (
              <Flex align="center" gap="3" py="2">
                <Avatar 
                  src={photoURL || undefined}
                  fallback={avatarFallback} 
                  size="3" 
                  radius="full" 
                  color="gray"
                  highContrast
                />
                <Box style={{ flexGrow: 1, overflow: 'hidden' }}> 
                  <Text as="div" size="2" weight="bold" style={{ color: 'var(--gray-12)', lineHeight: 'normal', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {displayName}
                  </Text>
                  <Text as="div" size="1" style={{ color: 'var(--gray-11)', lineHeight: 'normal', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {userEmail}
                  </Text>
                </Box>
              </Flex>
            )}

            <Button
              size="3"
              variant="soft"
              color="gray" 
              style={{ width: '100%', marginTop: 'var(--space-2)'}}
              onClick={handleLogout}
            >
              <ExitIcon width={18} height={18} style={{ marginRight: 'var(--space-2)' }} />
            Se déconnecter
            </Button>
          </Box>
        </Box>

        <Box p="6" style={{ flex: 1, backgroundColor: 'var(--gray-1)', minHeight: '100vh' }}>
        {children}
        </Box>
      </Flex>
    </Theme>
  );
}; 