"use client";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { GoogleSignInButton } from "./auth/GoogleSignInButton";
import { EmailSignInForm } from "./auth/EmailSignInForm";
import { UserProfile } from "./auth/UserProfile";
import { ErrorDisplay } from "./auth/ErrorDisplay";
import { Flex, Box } from "@radix-ui/themes";

interface AuthButtonsProps {
  mode?: "signup" | "login";
  highlight?: boolean;
}

export default function AuthButtons({ mode, highlight }: AuthButtonsProps) {
  const { user, loading } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const clearError = () => {
    setError(null);
  };

  if (loading) {
    return <Box>Chargement...</Box>;
  }

  // Si l'utilisateur est connecté et qu'aucun mode spécifique n'est demandé
  if (user && !mode) {
    return (
      <Box>
        <ErrorDisplay error={error} onDismiss={clearError} />
        <UserProfile user={user} onError={handleError} />
      </Box>
    );
  }

  // Mode spécifique : signup ou login
  if (mode === "signup" || mode === "login") {
    return (
      <Box>
        <ErrorDisplay error={error} onDismiss={clearError} />
        <GoogleSignInButton onError={handleError} />
      </Box>
    );
  }

  // Par défaut, afficher tous les boutons (mode "classique")
  return (
    <Flex direction="column" gap="3" style={{ minWidth: 300 }}>
      <ErrorDisplay error={error} onDismiss={clearError} />
      <GoogleSignInButton onError={handleError} />
      <EmailSignInForm onError={handleError} />
    </Flex>
  );
}