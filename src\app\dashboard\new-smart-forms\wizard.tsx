'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
// Imports optimisés - seulement les composants utilisés
import {
  Button, Flex, Text, Heading, Card, Badge, TextField, Box,
  Checkbox, IconButton, Dialog, TextArea, Tooltip, Spinner,
  Strong, Avatar, Grid, Separator, Link, Select, Switch, Callout
} from '@radix-ui/themes';
// Imports optimisés - seulement les icônes utilisées
import {
  PlusCircledIcon, MagnifyingGlassIcon, EyeOpenIcon, PaperPlaneIcon,
  CheckCircledIcon, CopyIcon, Cross2Icon, MagicWandIcon, ArrowLeftIcon,
  ArrowRightIcon, FileTextIcon, ReaderIcon, CalendarIcon, InfoCircledIcon,
  CrossCircledIcon, PersonIcon
} from '@radix-ui/react-icons';
import { formatDateFR } from '@/utils/dateUtils';
import { formatISO, addDays, isBefore, startOfDay } from 'date-fns';
import DatePicker from '@/components/DatePicker';
import RecipientTagInput, { Recipient, CrmSuggestion } from '@/components/RecipientTagInput';
import ReactSelect, { components, OptionProps, MultiValueProps, StylesConfig } from 'react-select';
import { OdooField, SmartForm } from '@/types/dashboard';
import { formLogger } from '@/utils/logger';
// Imports des données mock optimisées
import {
  mockTemplates,
  mockConnectedSources,
  mockEntitiesBySource,
  ConnectedDataSource,
  TargetEntity,
  FieldToCollect
} from '@/data/mockWizardData';
import { mockFieldsBySource } from '@/data/mockFieldsData';
import { generateFormUrl } from '@/utils/urlUtils';
import { useCreateSmartForm } from '@/hooks/useSmartForms';

// Types locaux spécifiques au wizard

interface Contact {
  id: string;
  name: string;
  email: string;
  role: string;
  companyId: string; // Changé en string pour correspondre aux IDs des entités
  instructions?: string;
}

interface SmartFormConfig {
  formId?: string; 
  publicFormId?: string; 
  title: string;
  description: string;
  selectedSourceIds: string[];
  targetEntityId: string;
  recipients: Recipient[];
  selectedFields: string[];
  deadline?: string;
  instructions?: string;
}

// Données mock importées depuis les fichiers séparés

// Toutes les données mock sont maintenant importées depuis les fichiers séparés

// Données mockEntitiesBySource importées depuis mockWizardData.ts

// Données mockFieldsBySource importées depuis mockFieldsData.ts

// Les données mock pour les contacts sont maintenant définies dans RecipientsStep

const getAllAvailableEntities = (): TargetEntity[] => {
  const allEntities: TargetEntity[] = [];
  Object.values(mockEntitiesBySource).forEach((entities: TargetEntity[]) => {
    allEntities.push(...entities);
  });
  return allEntities;
};

const getAllFieldsDetails = (): FieldToCollect[] => {
    const allFields: FieldToCollect[] = [];
  Object.values(mockFieldsBySource).forEach((sourceFields: FieldToCollect[]) => {
      allFields.push(...sourceFields);
    });
  return allFields;
};

type WizardStep = 'title-source' | 'entity' | 'fields' | 'recipients' | 'review';

const wizardStepsDisplay: Array<{ id: WizardStep; name: string }> = [
  { id: 'title-source', name: 'Titre & Source' },
  { id: 'entity', name: 'Entité Cible' },
  { id: 'fields', name: 'Champs à Collecter' },
  { id: 'recipients', name: 'Destinataires' },
  { id: 'review', name: 'Révision et Validation' },
];

const TitleSourceStep = ({ formConfig, setFormConfig, onNext, titleInputRef, sources }: {
  formConfig: SmartFormConfig;
  setFormConfig: React.Dispatch<React.SetStateAction<SmartFormConfig>>;
  onNext: () => void;
  titleInputRef: React.RefObject<HTMLInputElement | null>;
  sources: ConnectedDataSource[];
}) => {
  return (
  <Box style={{ minHeight: 'calc(100vh - 100px)', display: 'flex', flexDirection: 'column', justifyContent: 'center', paddingTop: 'var(--space-8)', paddingBottom: 'var(--space-8)' }}>
    <Box mb="8">
      <TextField.Root
        ref={titleInputRef}
        placeholder="Titre du SmartForm"
        value={formConfig.title}
        onChange={(e) => setFormConfig((prev: SmartFormConfig) => ({ ...prev, title: e.target.value }))}
        size="3"
        style={{
          fontSize: '32px',
          fontWeight: '800',
          fontFamily: 'var(--default-font-family)',
          letterSpacing: '-0.02em',
          border: 'none',
          boxShadow: 'none',
          backgroundColor: 'transparent',
          padding: '0',
          outline: 'none',
          color: formConfig.title ? 'var(--gray-12)' : 'var(--gray-8)'
        }}
      />
    </Box>

    <Box mb="8">
      <TextArea
        placeholder="Description optionnelle - permettra au destinataire de comprendre le contexte de l'utilisation de ces données"
        value={formConfig.description}
        onChange={(e) => setFormConfig((prev: SmartFormConfig) => ({ ...prev, description: e.target.value }))}
        rows={3}
        style={{
          border: 'none',
          boxShadow: 'none',
          backgroundColor: 'transparent',
          resize: 'vertical',
          fontSize: '16px',
          padding: '0',
          outline: 'none',
          color: 'var(--gray-11)'
        }}
      />
    </Box>

    {/* Section Source de données */}
    <Box mb="8">
      <Text size="4" weight="medium" mb="4" style={{ color: 'var(--gray-12)' }}>
        Quelles sources de données souhaitez-vous enrichir ?
      </Text>



      <Grid columns="1" gap="3" mb="4">
        {sources.map((source: ConnectedDataSource) => (
          <Card
            key={source.id}
            style={{
              cursor: 'pointer',
              padding: 'var(--space-4)',
              backgroundColor: (formConfig.selectedSourceIds || []).includes(source.id) ? 'var(--violet-3)' : 'var(--gray-2)',
              borderColor: (formConfig.selectedSourceIds || []).includes(source.id) ? 'var(--violet-7)' : 'var(--gray-6)',
              borderWidth: (formConfig.selectedSourceIds || []).includes(source.id) ? '2px' : '1px',
              transition: 'all 0.2s ease'
            }}
            onClick={() => {
              setFormConfig((prev: SmartFormConfig) => ({
                ...prev,
                selectedSourceIds: (prev.selectedSourceIds || []).includes(source.id)
                  ? (prev.selectedSourceIds || []).filter((id: string) => id !== source.id)
                  : [...(prev.selectedSourceIds || []), source.id],
                targetEntityId: '',
                selectedFields: []
              }));
            }}
          >
            <Flex align="center" gap="4">
              <img
                src={source.logoSrc}
                alt={source.name}
                style={{ width: '40px', height: '40px' }}
              />
              <Box style={{ flex: 1 }}>
                <Flex align="center" gap="2" mb="1">
                  <Text size="3" weight="medium">{source.name}</Text>
                  <Badge color="blue" variant="soft" size="1">{source.type}</Badge>
                </Flex>
                <Text size="2" color="gray">{source.totalFieldsAvailable} champs disponibles</Text>
              </Box>
              {(formConfig.selectedSourceIds || []).includes(source.id) && (
                <CheckCircledIcon width={20} height={20} style={{ color: 'var(--violet-9)' }} />
              )}
            </Flex>
          </Card>
        ))}
      </Grid>



      {/* Bouton Continuer */}
      <Flex justify="end" mt="8">
        <Button
          onClick={onNext}
          disabled={!(formConfig.selectedSourceIds || []).length || !formConfig.title.trim()}
          size="3"
        >
          Continuer
          <ArrowRightIcon width={16} height={16} />
        </Button>
      </Flex>
    </Box>
  </Box>
  );
};

const EntityStep = ({ formConfig, setFormConfig, entities, searchTerm, setSearchTerm, onNext }: {
  formConfig: SmartFormConfig;
  setFormConfig: React.Dispatch<React.SetStateAction<SmartFormConfig>>;
  entities: TargetEntity[];
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  onNext: () => void;
}) => {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const selectedEntity = entities.find(e => e.id === formConfig.targetEntityId);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setShowDropdown(true);
  };

  const handleSearchFocus = () => {
    setIsSearchFocused(true);
    setShowDropdown(true);
  };

  const handleSearchBlur = () => {
    setTimeout(() => {
      setIsSearchFocused(false);
      setShowDropdown(false);
    }, 200);
  };

  const handleEntitySelect = (entity: TargetEntity) => {
    setFormConfig((prev: SmartFormConfig) => ({ ...prev, targetEntityId: entity.id }));
    setSearchTerm(entity.name);
    setShowDropdown(false);
  };


  return (
    <Box style={{ paddingTop: 'var(--space-8)', paddingBottom: 'var(--space-8)' }}>
      <Box mb="8">
        <Heading size="6" mb="4">
          Pour quelle entreprise ou contact ?
        </Heading>
        <Box mb="3">
          <Text size="3" color="gray">
            Sélectionnez l'entité pour laquelle vous souhaitez collecter des informations
          </Text>
        </Box>
      </Box>

      {/* Champ de recherche avec dropdown */}
      <Box mb="6" style={{ position: 'relative' }}>
        <TextField.Root
          placeholder="Ex: Innovatech Ltd., Jean Dupont..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          onFocus={handleSearchFocus}
          onBlur={handleSearchBlur}
          size="3"
          style={{
            width: '100%',
            fontSize: '16px'
          }}
        >
          <TextField.Slot>
            <MagnifyingGlassIcon />
          </TextField.Slot>
        </TextField.Root>

        {/* Dropdown avec résultats */}
        {showDropdown && (
          <Card
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              zIndex: 1000,
              maxHeight: '300px',
              overflowY: 'auto',
              marginTop: '4px',
              border: '1px solid var(--gray-6)',
              backgroundColor: 'var(--color-background)'
            }}
          >
            {(() => {
              const filteredEntities = entities.filter((entity: TargetEntity) =>
                entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                entity.details.toLowerCase().includes(searchTerm.toLowerCase())
              );

              if (filteredEntities.length === 0) {
                return (
                  <Box p="3">
                    <Text size="2" color="gray">Aucun résultat trouvé</Text>
                  </Box>
                );
              }

              return (
                <Box p="2">
                  {filteredEntities.slice(0, 5).map((entity: TargetEntity) => {
                    const entitySource = mockConnectedSources.find((s: ConnectedDataSource) => s.id === entity.sourceId);
                    return (
                      <Box
                        key={entity.id}
                        p="3"
                        style={{
                          cursor: 'pointer',
                          borderRadius: 'var(--radius-2)',
                          transition: 'background-color 0.2s'
                        }}
                        onClick={() => handleEntitySelect(entity)}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--gray-3)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <Flex align="center" gap="3">
                          <Text style={{ fontSize: '20px' }}>{entity.avatar}</Text>
                          <Box style={{ flex: 1 }}>
                            <Flex align="center" gap="2" mb="1">
                              <Text size="3" weight="medium">{entity.name}</Text>
                              <Badge color="blue" variant="soft" size="1">
                                {entity.type === 'company' ? 'Entreprise' : 'Contact'}
                              </Badge>
                              {entitySource && (
                                <Badge color="gray" variant="soft" size="1">{entitySource.name}</Badge>
                              )}
                            </Flex>
                            <Text size="2" color="gray">{entity.details}</Text>
                          </Box>
                        </Flex>
                      </Box>
                    );
                  })}
                </Box>
              );
            })()}
          </Card>
        )}
      </Box>

      {/* Entités suggérées */}
      {!searchTerm && (
        <Box mb="6">
          <Text size="3" weight="medium" mb="3">Entités suggérées</Text>
          <Grid columns="1" gap="3">
            {(() => {
              const exampleEntities = entities.slice(0, 3);
              return (
                <>
                  {exampleEntities.map((entity: TargetEntity) => {
                    const entitySource = mockConnectedSources.find((s: ConnectedDataSource) => s.id === entity.sourceId);
                    return (
                      <Box
                        key={entity.id}
                        p="4"
                        style={{
                          cursor: 'pointer',
                          border: formConfig.targetEntityId === entity.id ? '2px solid var(--violet-7)' : '1px solid var(--gray-6)',
                          borderRadius: 'var(--radius-3)',
                          backgroundColor: formConfig.targetEntityId === entity.id ? 'var(--violet-2)' : 'var(--gray-1)',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={() => {
                          setFormConfig((prev: SmartFormConfig) => ({ ...prev, targetEntityId: entity.id }));
                          setSearchTerm(entity.name);
                        }}
                      >
                        <Flex align="center" gap="4">
                          <Text style={{ fontSize: '32px' }}>{entity.avatar}</Text>
                          <Box style={{ flex: 1 }}>
                            <Flex align="center" gap="2" mb="2">
                              <Text size="4" weight="medium">{entity.name}</Text>
                              <Badge color="blue" variant="soft" size="1">
                                {entity.type === 'company' ? 'Entreprise' : 'Contact'}
                              </Badge>
                              {entitySource && (
                                <Badge color="gray" variant="soft" size="1">{entitySource.name}</Badge>
                              )}
                            </Flex>
                            <Text size="3" color="gray" mb="2">{entity.details}</Text>
                            <Flex align="center" gap="4">
                              <Text size="2" color="gray">
                                Complétude: {Math.round(entity.completionRate * 100)}%
                              </Text>
                              {entity.lastActivity && (
                                <Text size="2" color="gray">
                                  Dernière activité: {entity.lastActivity}
                                </Text>
                              )}
                            </Flex>
                          </Box>
                          {formConfig.targetEntityId === entity.id && (
                            <CheckCircledIcon width={24} height={24} style={{ color: 'var(--violet-9)' }} />
                          )}
                        </Flex>
                      </Box>
                    );
                  })}
                </>
              );
            })()}
          </Grid>
        </Box>
      )}

      {/* Entité sélectionnée */}
      {selectedEntity && (
        <Box mb="6">
          <Text size="3" weight="medium" mb="3">Entité sélectionnée</Text>
          <Card style={{ padding: 'var(--space-4)', backgroundColor: 'var(--green-2)', borderColor: 'var(--green-7)' }}>
            <Flex align="center" gap="4">
              <Text style={{ fontSize: '32px' }}>{selectedEntity.avatar}</Text>
              <Box style={{ flex: 1 }}>
                <Flex align="center" gap="2" mb="1">
                  <Text size="4" weight="medium">{selectedEntity.name}</Text>
                  <Badge color="green" variant="soft" size="1">Sélectionné</Badge>
                </Flex>
                <Text size="3" color="gray">{selectedEntity.details}</Text>
              </Box>
            </Flex>
          </Card>
        </Box>
      )}

      {/* Bouton Suivant */}
      <Flex justify="end" mt="8">
        <Button
          onClick={onNext}
          disabled={!formConfig.targetEntityId}
          size="3"
        >
          Suivant
          <ArrowRightIcon width={16} height={16} />
        </Button>
      </Flex>
    </Box>
  );
};

const FieldsStep = ({ formConfig, setFormConfig, fields, searchTerm, setSearchTerm, templates, onNext }: {
  formConfig: SmartFormConfig;
  setFormConfig: React.Dispatch<React.SetStateAction<SmartFormConfig>>;
  fields: FieldToCollect[];
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  templates: any[];
  onNext: () => void;
}) => {
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);

  const handleFieldToggle = (fieldId: string) => {
    setFormConfig((prev: SmartFormConfig) => ({
      ...prev,
      selectedFields: (prev.selectedFields || []).includes(fieldId)
        ? (prev.selectedFields || []).filter((id: string) => id !== fieldId)
        : [...(prev.selectedFields || []), fieldId]
    }));
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    // Mapping des modèles vers les champs correspondants (basé sur les vrais IDs)
    const templateFieldMappings: { [key: string]: string[] } = {
      'billing-template': [
        'qb_company_name', 'qb_billing_address', 'qb_tax_id', 'qb_payment_terms',
        'odoo_company_name'
      ],
      'contact-template': [
        'qb_contact_email',
        'odoo_contact_email', 'odoo_phone'
      ],
      'sales-template': [
        'qb_company_name', 'qb_contact_email',
        'odoo_company_name', 'odoo_contact_email', 'odoo_industry', 'odoo_employee_count'
      ],
      'linkedin-template': [
        'qb_company_name',
        'odoo_company_name', 'odoo_website', 'odoo_industry', 'odoo_employee_count'
      ],
      'marketing-template': [
        'qb_contact_email',
        'odoo_contact_email', 'odoo_industry', 'odoo_website'
      ],
      'onboarding-template': [
        'qb_company_name', 'qb_contact_email', 'qb_billing_address',
        'odoo_company_name', 'odoo_contact_email', 'odoo_phone'
      ],
      'tech-template': [
        'odoo_website', 'odoo_industry'
      ],
      'compliance-template': [
        'qb_tax_id', 'qb_billing_address',
        'odoo_company_name'
      ]
    };

    // Récupérer les champs correspondant au template
    const templateFields = templateFieldMappings[templateId] || [];

    // Filtrer les champs qui existent réellement dans les champs disponibles
    const availableTemplateFields = templateFields.filter(fieldId =>
      fields.some(field => field.id === fieldId)
    );

    // Sélectionner automatiquement ces champs
    setFormConfig((prev: SmartFormConfig) => ({
      ...prev,
      selectedFields: [...new Set([...(prev.selectedFields || []), ...availableTemplateFields])]
    }));

    console.log(`Template "${template.name}" appliqué: ${availableTemplateFields.length} champs sélectionnés`);
  };

  return (
    <Box style={{ paddingTop: 'var(--space-8)', paddingBottom: 'var(--space-8)' }}>
      <Box mb="8">
        <Heading size="6" mb="4">
          Quels champs souhaitez-vous collecter ?
        </Heading>
        <Text size="3" color="gray">
          Sélectionnez les informations que vous souhaitez demander
        </Text>
      </Box>

      {/* Modèles prédéfinis */}
      <Box mb="6">
        <Text size="3" weight="medium" mb="3">Modèles prédéfinis</Text>
        <Select.Root onValueChange={handleTemplateSelect}>
          <Select.Trigger placeholder="Choisir un modèle..." style={{ width: '100%' }} />
          <Select.Content>
            {templates.map((template) => (
              <Select.Item key={template.id} value={template.id}>
                {template.icon} {template.name} ({template.fieldsCount} champs)
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
      </Box>

      {/* Champ de recherche */}
      <Box mb="6">
        <TextField.Root
          placeholder="Rechercher un champ..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="3"
          style={{ width: '100%' }}
        >
          <TextField.Slot>
            <MagnifyingGlassIcon />
          </TextField.Slot>
        </TextField.Root>
      </Box>

      {/* Champs disponibles */}
      <Box mb="6">
        <Text size="3" weight="medium" mb="3">
          Champs disponibles ({fields.filter(field =>
            !searchTerm ||
            field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            field.description.toLowerCase().includes(searchTerm.toLowerCase())
          ).length})
        </Text>
        <Grid columns="1" gap="3">
          {fields
            .filter(field =>
              !searchTerm ||
              field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              field.description.toLowerCase().includes(searchTerm.toLowerCase())
            )
            .map((field: FieldToCollect) => {
              const isSelected = (formConfig.selectedFields || []).includes(field.id);
              return (
                <Card
                  key={field.id}
                  style={{
                    cursor: 'pointer',
                    padding: 'var(--space-4)',
                    backgroundColor: isSelected ? 'var(--violet-3)' : 'var(--gray-1)',
                    borderColor: isSelected ? 'var(--violet-7)' : 'var(--gray-6)',
                    borderWidth: isSelected ? '2px' : '1px',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => handleFieldToggle(field.id)}
                >
                  <Flex align="center" gap="4">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleFieldToggle(field.id)}
                    />
                    <Box style={{ flex: 1 }}>
                      <Flex align="center" gap="2" mb="1">
                        <Text size="3" weight="medium">{field.name}</Text>
                        {field.required && (
                          <Badge color="red" variant="soft" size="1">Requis</Badge>
                        )}
                        <Badge color="gray" variant="soft" size="1">{field.sourceName}</Badge>
                      </Flex>
                      <Text size="2" color="gray" mb="2">{field.description}</Text>
                      {field.currentValue && (
                        <Text size="2" color="green">
                          Valeur actuelle: {field.currentValue}
                        </Text>
                      )}
                    </Box>
                    <Badge
                      color={field.completionStatus === 'complete' ? 'green' :
                             field.completionStatus === 'partial' ? 'yellow' : 'gray'}
                      variant="soft"
                      size="1"
                    >
                      {field.completionStatus === 'complete' ? 'Complet' :
                       field.completionStatus === 'partial' ? 'Partiel' : 'Vide'}
                    </Badge>
                  </Flex>
                </Card>
              );
            })}
        </Grid>
      </Box>

      {/* Bouton Suivant */}
      <Flex justify="end" mt="8">
        <Button
          onClick={onNext}
          disabled={!(formConfig.selectedFields || []).length}
          size="3"
        >
          Suivant
          <ArrowRightIcon width={16} height={16} />
        </Button>
      </Flex>
    </Box>
  );
};

const RecipientsStep = ({ formConfig, setFormConfig, onNext }: {
  formConfig: SmartFormConfig;
  setFormConfig: React.Dispatch<React.SetStateAction<SmartFormConfig>>;
  onNext: () => void;
}) => {
  const [showDeadlinePicker, setShowDeadlinePicker] = React.useState(!!formConfig.deadline);

  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [suggestedContacts, setSuggestedContacts] = useState<Recipient[]>([]);

  const handleAddRecipient = (recipient: Recipient) => {
    setFormConfig((prev: SmartFormConfig) => ({
      ...prev,
      recipients: [...(prev.recipients || []), recipient]
    }));
  };

  const handleRemoveRecipient = (email: string) => {
    setFormConfig((prev: SmartFormConfig) => ({
      ...prev,
      recipients: (prev.recipients || []).filter((r: Recipient) => r.email !== email)
    }));
  };

  // Récupération de l'entité sélectionnée depuis la configuration
  const selectedEntity = React.useMemo(() => {
    if (!formConfig.targetEntityId) return null;
    // Récupérer l'entité depuis les données mock
    const allEntities = Object.values(mockEntitiesBySource).flat();
    return allEntities.find(entity => entity.id === formConfig.targetEntityId) || null;
  }, [formConfig.targetEntityId]);

  // Contacts mock pour les suggestions (basé sur les entités réelles)
  const mockContacts: Contact[] = React.useMemo(() => [
    // Contacts pour Onyx Trading
    { id: 1, name: 'Julien Deprins', email: '<EMAIL>', role: 'Sales Manager', companyId: 'onyx_trading' },
    { id: 2, name: 'Sophie Laurent', email: '<EMAIL>', role: 'Directrice Financière', companyId: 'onyx_trading' },
    { id: 3, name: 'Marc Dubois', email: '<EMAIL>', role: 'Responsable Logistique', companyId: 'onyx_trading' },

    // Contacts pour Innovatech
    { id: 4, name: 'Marie Martin', email: '<EMAIL>', role: 'Directrice Financière', companyId: 'innovatech' },
    { id: 5, name: 'Thomas Bernard', email: '<EMAIL>', role: 'CTO', companyId: 'innovatech' },

    // Contacts pour Global Logistics
    { id: 6, name: 'Pierre Durand', email: '<EMAIL>', role: 'Responsable Opérations', companyId: 'global_logistics' },
    { id: 7, name: 'Isabelle Moreau', email: '<EMAIL>', role: 'Directrice Commerciale', companyId: 'global_logistics' },

    // Contacts pour TechStartup Inc.
    { id: 8, name: 'Sarah Johnson', email: '<EMAIL>', role: 'CEO', companyId: 'tech_startup' },
    { id: 9, name: 'David Chen', email: '<EMAIL>', role: 'Lead Developer', companyId: 'tech_startup' },

    // Contacts pour Synergy Corporation
    { id: 10, name: 'Robert Smith', email: '<EMAIL>', role: 'Managing Director', companyId: 'synergy_corp' },
    { id: 11, name: 'Emma Wilson', email: '<EMAIL>', role: 'Senior Consultant', companyId: 'synergy_corp' }
  ], []);

  // Récupérer les contacts suggérés pour l'entreprise sélectionnée
  React.useEffect(() => {
    if (selectedEntity && selectedEntity.type === 'company') {
      setIsLoadingSuggestions(true);

      // Simuler un délai de chargement
      const timer = setTimeout(() => {
        const companyContacts = mockContacts.filter(c => c.companyId === selectedEntity.id);
        setSuggestedContacts(companyContacts.map(c => ({
          key: c.id.toString(),
          name: c.name,
          email: c.email,
          role: c.role,
          type: 'crm' as const // Changé en 'crm' pour correspondre au type Recipient
        })));
        setIsLoadingSuggestions(false);
      }, 500);

      return () => clearTimeout(timer);
    } else {
      setSuggestedContacts([]);
      setIsLoadingSuggestions(false);
    }
  }, [selectedEntity, mockContacts]);

  // Pré-remplissage de l'email du contact si selectedEntity est un contact
  React.useEffect(() => {
    if (selectedEntity && selectedEntity.type === 'contact' && selectedEntity.email && (formConfig.recipients || []).length === 0) {
      const contactRecipient: Recipient = {
        key: selectedEntity.id,
        name: selectedEntity.name,
        email: selectedEntity.email,
        type: 'crm' // Changé en 'crm' pour correspondre au type Recipient
      };

      setFormConfig((prev: SmartFormConfig) => ({
        ...prev,
        recipients: [contactRecipient, ...(prev.recipients || []).filter(r => r.email.toLowerCase() !== selectedEntity.email!.toLowerCase())]
      }));
    }
  }, [selectedEntity, formConfig.recipients, setFormConfig]);

  return (
    <Box style={{ paddingTop: 'var(--space-8)', paddingBottom: 'var(--space-8)' }}>
      <Box mb="8">
        <Heading size="6" mb="4">
          Qui va recevoir ce SmartForm ?
        </Heading>
        <Text size="3" color="gray">
          Ajoutez les destinataires qui recevront le formulaire à compléter
        </Text>
      </Box>

      {/* Composant de saisie des destinataires */}
      <Box mb="6">
        <RecipientTagInput
          recipients={formConfig.recipients || []}
          onRecipientsChange={(recipients) => setFormConfig(prev => ({ ...prev, recipients }))}
          placeholder="Ajouter un destinataire..."
          crmSuggestions={suggestedContacts}
        />
      </Box>

      {/* Contacts suggérés */}
      {suggestedContacts.length > 0 && (
        <Box mb="6">
          <Text size="3" weight="medium" mb="3">Contacts suggérés</Text>
          <Flex gap="2" wrap="wrap">
            {suggestedContacts.map((contact: Recipient) => {
              const isAdded = (formConfig.recipients || []).some(r => r.email.toLowerCase() === contact.email.toLowerCase());
              return (
                <Tooltip content={contact.email} key={contact.key}>
                  <Button
                    variant={isAdded ? "solid" : "outline"}
                    color={isAdded ? "green" : "gray"}
                    size="2"
                    disabled={isAdded}
                    onClick={() => !isAdded && handleAddRecipient(contact)}
                  >
                    {isAdded && <CheckCircledIcon width={14} height={14} />}
                    {contact.name}
                  </Button>
                </Tooltip>
              );
            })}
          </Flex>
        </Box>
      )}

      {/* Options de délai */}
      <Box mb="6">
        <Flex align="center" gap="3" mb="3">
          <Switch
            checked={showDeadlinePicker}
            onCheckedChange={(checked) => {
              setShowDeadlinePicker(checked);
              if (!checked) {
                setFormConfig((prev: SmartFormConfig) => ({ ...prev, deadline: undefined }));
              }
            }}
          />
          <Text size="3" weight="medium">Définir une date limite de réponse</Text>
        </Flex>

        {showDeadlinePicker && (
          <Box ml="6">
            <DatePicker
              value={formConfig.deadline}
              onChange={(date) => setFormConfig((prev: SmartFormConfig) => ({ ...prev, deadline: date }))}
              placeholder="Sélectionner une date limite"
            />
          </Box>
        )}
      </Box>

      {/* Bouton Suivant */}
      <Flex justify="end" mt="8">
        <Button
          onClick={onNext}
          disabled={!(formConfig.recipients || []).length}
          size="3"
        >
          Suivant
          <ArrowRightIcon width={16} height={16} />
        </Button>
      </Flex>
    </Box>
  );
};

// Types pour les interfaces
interface Contact {
  id: number;
  name: string;
  email: string;
  role: string;
  companyId: string;
}

interface SmartFormConfig {
  title: string;
  description: string;
  selectedSourceIds: string[];
  targetEntityId: string;
  selectedFields: string[];
  recipients: Recipient[];
  deadline?: string;
  instructions?: string;
}

interface FormPreviewRendererProps {
  formPreviewData: {
    title: string;
    description?: string;
    fields: Array<{
      id: string;
      name: string;
      description: string;
      required: boolean;
      type: string;
      options?: string[];
      placeholder?: string;
      example?: string;
      rows?: number;
    }>;
    recipients: Recipient[];
    deadline?: string;
    instructions?: string;
  };
}

const FormPreviewRenderer = ({ formPreviewData }: FormPreviewRendererProps) => {

  return (
    <Box style={{ border: '1px solid var(--gray-a3)', borderRadius: 'var(--radius-3)', padding: 'var(--space-4)', backgroundColor: 'var(--gray-a1)' }}>
      <Heading as="h3" size="6" align="center" mb="3" style={{ color: 'var(--gray-12)'}}>{formPreviewData.title}</Heading>

      {formPreviewData.description && (
        <Text size="3" align="center" mb="4" style={{ color: 'var(--gray-11)' }}>
          {formPreviewData.description}
        </Text>
      )}

      <Box mb="4">
        {formPreviewData.fields.map((field, index) => (
          <Box key={field.id} mb="4" p="3" style={{ border: '1px solid var(--gray-a6)', borderRadius: 'var(--radius-2)', backgroundColor: 'var(--color-background)' }}>
            <Flex align="center" gap="2" mb="2">
              <Text size="3" weight="medium" style={{ color: 'var(--gray-12)' }}>
                {field.name}
                {field.required && <Text color="red" ml="1">*</Text>}
              </Text>
            </Flex>

            <Text size="2" mb="3" style={{ color: 'var(--gray-11)' }}>
              {field.description}
            </Text>

            {field.type === 'textarea' ? (
              <TextArea
                placeholder={field.placeholder || `Saisissez ${field.name.toLowerCase()}`}
                rows={field.rows || 3}
                disabled
                style={{ backgroundColor: 'var(--gray-a2)' }}
              />
            ) : field.type === 'select' && field.options ? (
              <Select.Root disabled>
                <Select.Trigger placeholder={field.placeholder || `Sélectionnez ${field.name.toLowerCase()}`} />
              </Select.Root>
            ) : (
              <TextField.Root
                placeholder={field.placeholder || `Saisissez ${field.name.toLowerCase()}`}
                type={field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : 'text'}
                disabled
                style={{ backgroundColor: 'var(--gray-a2)' }}
              />
            )}

            {field.example && (
              <Text size="1" mt="1" style={{ color: 'var(--gray-9)' }}>
                Exemple: {field.example}
              </Text>
            )}
          </Box>
        ))}
      </Box>

      <Separator mb="4" />

      <Flex justify="between" align="center">
        <Text size="2" style={{ color: 'var(--gray-11)' }}>
          Destinataires: {formPreviewData.recipients.map(r => r.name).join(', ')}
        </Text>
        {formPreviewData.deadline && (
          <Text size="2" style={{ color: 'var(--gray-11)' }}>
            Échéance: {formPreviewData.deadline}
          </Text>
        )}
      </Flex>
    </Box>
  );
};

const ReviewStep = ({
  formConfig,
  setFormConfig,
  onSubmit,
  onBack,
  isSubmitting,
  publicFormId,
  generatedLink,
  onCopyLinkProp,
  onPreviewLinkProp,
  onNewForm
}: {
  formConfig: SmartFormConfig;
  setFormConfig: React.Dispatch<React.SetStateAction<SmartFormConfig>>;
  onSubmit: () => void;
  onBack: () => void;
  isSubmitting: boolean;
  publicFormId?: string;
  generatedLink?: string;
  onCopyLinkProp: (link: string) => void;
  onPreviewLinkProp: (link: string) => void;
  onNewForm: () => void;
}) => {
  const [copied, setCopied] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [formPreviewData, setFormPreviewData] = useState<FormPreviewRendererProps['formPreviewData'] | null>(null);

  // Récupérer les détails complets de tous les champs disponibles une seule fois
  const allFieldsAvailable = React.useMemo(() => [], []); // À définir selon votre logique

  // Récupérer les champs sélectionnés avec leurs détails complets
  const selectedFieldObjects = React.useMemo(() => {
    return formConfig.selectedFields
      .map(fieldId => allFieldsAvailable.find(f => f.id === fieldId))
      .filter(Boolean) as FieldToCollect[];
  }, [formConfig.selectedFields, allFieldsAvailable]);

  // Calculer les statistiques des champs
  const requiredFieldsCount = React.useMemo(() => {
    return selectedFieldObjects.filter(field => field.required).length;
  }, [selectedFieldObjects]);

  const optionalFieldsCount = React.useMemo(() => {
    return selectedFieldObjects.filter(field => !field.required).length;
  }, [selectedFieldObjects]);

  const fieldsToReviewCount = React.useMemo(() => {
    return selectedFieldObjects.filter(
      field => field.completionStatus === 'complete' || field.completionStatus === 'partial'
    ).length;
  }, [selectedFieldObjects]);

  const handleCopyLinkInternal = () => {
    if (generatedLink) {
      navigator.clipboard.writeText(generatedLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handlePreviewInternal = () => {
    if (generatedLink) {
      window.open(generatedLink, '_blank');
      onPreviewLinkProp(generatedLink);
    }
  };

  // Fonction manquante
  const getAllFieldsDetails = () => {
    return []; // À définir selon votre logique
  };

  const handleOpenFormPreview = () => {
    const allFieldsAvailable = getAllFieldsDetails();
    const selectedFieldDetailsForPreview = formConfig.selectedFields
      .map(fieldId => {
        const fieldDetail = allFieldsAvailable.find(f => f.id === fieldId);
        if (!fieldDetail) return null;
        return {
          id: fieldDetail.id,
          name: fieldDetail.name,
          description: fieldDetail.description,
          type: fieldDetail.type,
          required: fieldDetail.required,
          options: fieldDetail.options,
          placeholder: fieldDetail.placeholder,
          example: fieldDetail.example,
          rows: fieldDetail.type === 'textarea' ? 3 : undefined,
        };
      })
      .filter(Boolean) as FormPreviewRendererProps['formPreviewData']['fields'];

    const previewData: FormPreviewRendererProps['formPreviewData'] = {
      title: formConfig.title || "Titre du formulaire (Aperçu)",
      description: formConfig.description,
      fields: selectedFieldDetailsForPreview,
      recipients: formConfig.recipients || [],
      deadline: formConfig.deadline,
      instructions: formConfig.instructions,
    };
    setFormPreviewData(previewData);
    setIsPreviewDialogOpen(true);
  };

  if (publicFormId && generatedLink) {
    return (
      <Box style={{ width: '100%', maxWidth: '600px', margin: 'auto', marginTop: 'var(--space-6)' }}>
        <Flex direction="column" gap="4" align="center" p="5">
          <CheckCircledIcon width="60" height="60" style={{ color: 'var(--green-9)' }} />
          <Heading as="h2" size="7">Formulaire Créé !</Heading>
          <Text color="gray" align="center">
            {'Votre formulaire "'}
            <Strong>{formConfig.title}</Strong>
            {'" a été créé avec succès. Vous pouvez maintenant partager le lien suivant avec vos destinataires :'}
          </Text>

          <Flex
            align="center"
            gap="2"
            p="2"
            style={{
              border: '1px solid var(--gray-6)',
              borderRadius: 'var(--radius-3)',
              backgroundColor: 'var(--gray-1)',
              width: '100%'
            }}
          >
            <Link
              href={generatedLink}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                flexGrow: 1,
                overflowWrap: 'break-word',
                wordBreak: 'break-all',
                fontSize: 'var(--font-size-2)',
                color: 'var(--blue-11)'
              }}
            >
              {generatedLink}
            </Link>
            <Tooltip content={copied ? "Copié !" : "Copier le lien"}>
              <IconButton
                variant="ghost"
                color={copied ? "green" : "gray"}
                onClick={handleCopyLinkInternal}
                size="2"
              >
                {copied ? <CheckCircledIcon /> : <CopyIcon />}
              </IconButton>
            </Tooltip>
          </Flex>

          <Flex gap="3" mt="4" width="100%">
            <Tooltip content="Ouvrir le formulaire public dans un nouvel onglet">
              <Button onClick={handlePreviewInternal} variant="outline" style={{ flexGrow: 1 }}>
                <EyeOpenIcon /> Prévisualiser Lien Public
              </Button>
            </Tooltip>
            <Tooltip content="Fonctionnalité à venir">
              <Button variant="outline" style={{ flexGrow: 1 }} disabled>
                <PaperPlaneIcon /> Envoyer par email
              </Button>
            </Tooltip>
          </Flex>

          <Separator my="4" size="4" />

          <Button onClick={onNewForm} size="3">
            <PlusCircledIcon /> Créer un autre formulaire
          </Button>
        </Flex>
      </Box>
    );
  }

  return (
    <Box style={{ paddingTop: 'var(--space-8)', paddingBottom: 'var(--space-8)' }}>
      <Box mb="8">
        <Heading size="7" mb="2">
          Révision et Validation
        </Heading>
        <Text color="orange" size="3" mb="4" style={{ display: 'block', padding: 'var(--space-3)', backgroundColor: 'var(--orange-a3)', borderRadius: 'var(--radius-2)' }}>
          <Strong>Attention :</Strong> Toutes les informations qui suivent (titre, description, champs demandés, instructions, date d'échéance) seront visibles par le(s) destinataire(s).
        </Text>
        <Text size="3" color="gray">
          Relisez attentivement les détails de votre SmartForm avant de l'envoyer.
        </Text>
      </Box>

      <Card style={{ padding: 'var(--space-4)', marginBottom: 'var(--space-6)' }}>
        <Heading size="5" mb="1">
          <Strong>Titre :</Strong> {formConfig.title || '(Aucun titre défini)'}
        </Heading>
        {formConfig.description && (
          <Text size="2" color="gray" mb="3" style={{ whiteSpace: 'pre-wrap'}}>
            {formConfig.description}
          </Text>
        )}

        <Grid columns={{ initial: '1', sm: '2' }} gap="4" mb="4">
          <Box>
            <Text as="div" size="2" weight="bold" mb="2">Destinataires ({(formConfig.recipients || []).length}) :</Text>
            {(formConfig.recipients || []).length > 0 ? (
              <Flex direction="column" gap="1">
                {(formConfig.recipients || []).map(recipient => (
                  <Badge key={recipient.key || recipient.email} color="gray" variant="soft" style={{width: 'fit-content'}}>
                    {recipient.name ? `${recipient.name} <${recipient.email}>` : recipient.email}
                  </Badge>
                ))}
              </Flex>
            ) : (
              <Text size="2" color="gray">(Aucun destinataire défini)</Text>
            )}
          </Box>

          <Box>
            <Text as="div" size="2" weight="bold" mb="2">Champs à collecter :</Text>
            <Flex direction="column" gap="1">
              <Text size="2"><Strong>Total :</Strong> {selectedFieldObjects.length}</Text>
              <Text size="2"><Strong>Obligatoires :</Strong> {requiredFieldsCount}</Text>
              <Text size="2"><Strong>Optionnels :</Strong> {optionalFieldsCount}</Text>
              <Text size="2"><Strong>Données existantes :</Strong> {fieldsToReviewCount}</Text>
            </Flex>
          </Box>
        </Grid>

        {formConfig.deadline && (
          <Box mt="3">
            <Text size="2">
              <Strong>Date d'échéance souhaitée:</Strong> {new Date(formConfig.deadline + 'T00:00:00').toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </Text>
          </Box>
        )}
      </Card>

      {/* Détails des champs sélectionnés */}
      {selectedFieldObjects.length > 0 && (
        <Box mb="6">
          <Heading size="4" mb="3">Détail des champs ({selectedFieldObjects.length}) :</Heading>
          <Card>
            <Flex direction="column" gap="3" p="3">
              {selectedFieldObjects.map(field => (
                <Box key={field.id} style={{ borderBottom: '1px solid var(--gray-a3)', paddingBottom: 'var(--space-2)', marginBottom: 'var(--space-2)' }}>
                  <Flex align="start" justify="between" mb="1" direction={{initial: 'column', xs: 'row'}} gap="1">
                    <Flex align="center" gap="2" wrap="wrap">
                      <Text weight="medium" size="3">{field.name}</Text>
                      <Badge variant="outline" color="gray" size="1">{field.type}</Badge>
                      <Text size="1" color="gray">({field.sourceName})</Text>
                    </Flex>
                    <Flex gap="2" align="center" style={{ flexShrink: 0 }}>
                      {field.required
                        ? <Badge color="orange" variant="solid" size="1">Requis</Badge>
                        : <Badge color="gray" variant="soft" size="1">Optionnel</Badge>}
                      <Badge
                        color={
                          field.completionStatus === 'complete' ? 'green' :
                          field.completionStatus === 'partial' ? 'yellow' : 'gray'
                        }
                        variant="soft"
                        size="1"
                      >
                        {field.completionStatus === 'complete' ? 'Existant' :
                         field.completionStatus === 'partial' ? 'Partiel' : 'Vide'}
                      </Badge>
                    </Flex>
                  </Flex>
                  {field.currentValue && (
                    <Text size="1" color="blue" style={{ fontStyle: 'italic' }}>
                      <Strong>Donnée existante :</Strong> {field.currentValue.length > 60 ?
                        `${field.currentValue.substring(0, 60)}...` : field.currentValue}
                    </Text>
                  )}
                </Box>
              ))}
            </Flex>
          </Card>
        </Box>
      )}

      <Box mb="6">
        <Box mb="3">
          <Flex direction="column" gap="1">
            <Text size="3" weight="medium">
              Instructions personnalisées (optionnel)
            </Text>
            <Text size="2" color="gray">
              Ces instructions apparaîtront en haut du formulaire pour le destinataire.
            </Text>
          </Flex>
        </Box>
        <TextArea
          placeholder="Ex: Merci de remplir ce formulaire avant le [date]. N'hésitez pas à nous contacter si vous avez des questions."
          value={formConfig.instructions || ''}
          onChange={(e) => setFormConfig((prev: SmartFormConfig) => ({ ...prev, instructions: e.target.value }))}
          rows={4}
          style={{
            border: '1px solid var(--gray-6)',
            boxShadow: 'none',
            backgroundColor: 'var(--gray-1)',
            borderRadius: 'var(--radius-2)'
          }}
        />
      </Box>

      <Flex justify="between" align="center" gap="3" mt="8">
        <Link
          href="#"
          onClick={(e) => { e.preventDefault(); onBack(); }}
          size="3"
          style={{
            color: 'var(--gray-11)',
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: 'var(--space-2)'
          }}
        >
          <ArrowLeftIcon />
          Retour
        </Link>
        <Flex gap="3">
          <Button variant="outline" color="gray" size="3" onClick={handleOpenFormPreview}>
            <EyeOpenIcon />
            Prévisualiser
          </Button>
          <Button size="3" onClick={onSubmit} disabled={isSubmitting}>
            {isSubmitting ? <Spinner size="2" /> : <CheckCircledIcon />}
            {isSubmitting ? ' Finalisation...' : ' Valider et Envoyer'}
          </Button>
        </Flex>
      </Flex>

      {/* Modale de prévisualisation */}
      <Dialog.Root open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <Dialog.Content style={{ maxWidth: '750px', width:'90vw', maxHeight: '85vh', display: 'flex', flexDirection: 'column' }}>
          <Dialog.Title>Aperçu du Formulaire</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            Voici comment le formulaire apparaîtra pour le destinataire. Les champs sont désactivés pour cet aperçu.
          </Dialog.Description>

          <Box style={{ flexGrow: 1, overflowY: 'auto', paddingRight: 'var(--space-3)', marginRight: '-var(--space-3)' }}>
            {formPreviewData ? (
              <FormPreviewRenderer formPreviewData={formPreviewData} />
            ) : (
              <Flex align="center" justify="center" style={{minHeight: '200px'}}><Spinner /> Chargement de l'aperçu...</Flex>
            )}
          </Box>

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Fermer
              </Button>
            </Dialog.Close>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </Box>
  );
};

// --- COMPOSANT DE NAVIGATION DES ETAPES ---
interface StepperNavigationProps {
  currentStep: WizardStep;
  setCurrentStep: (step: WizardStep) => void;
  steps: Array<{ id: WizardStep; name: string }>;
  maxReachedStepIndex: number;
}

const StepperNavigation: React.FC<StepperNavigationProps> = ({
  currentStep,
  setCurrentStep,
  steps,
  maxReachedStepIndex,
}) => {
  const currentStepIndex = steps.findIndex(s => s.id === currentStep);

  return (
    <Flex
      align="center"
      justify="center"
      gap="3"
      py="3"
      px="4"
      wrap="nowrap"
      style={{
        overflowX: 'auto',
        width: '100%',
      }}
    >
      {steps.map((step, index) => {
        const isActive = index === currentStepIndex;
        const isReachable = index <= maxReachedStepIndex;

        return (
          <React.Fragment key={step.id}>
            <Button
              variant={isActive ? "solid" : "outline"}
              color={isActive ? undefined : "gray"}
              disabled={!isReachable}
              onClick={() => isReachable && setCurrentStep(step.id)}
              style={{
                cursor: isReachable ? 'pointer' : 'not-allowed',
                opacity: isReachable ? 1 : 0.6,
                justifyContent: 'center',
                paddingLeft: 'var(--space-3)',
                paddingRight: 'var(--space-3)',
              }}
            >
              <Flex align="center" gap="2">
                <Text size="2">{index + 1}.</Text>
                <Text size="2">{step.name}</Text>
              </Flex>
            </Button>
            {index < steps.length - 1 && (
              <Separator orientation="vertical" size="2" style={{ height: '20px', backgroundColor: 'var(--gray-a6)' }} />
            )}
          </React.Fragment>
        );
      })}
    </Flex>
  );
};

// --- COMPOSANT PRINCIPAL WIZARD ---
export default function SmartFormWizard() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<WizardStep>('title-source');
  const [maxReachedStepIndex, setMaxReachedStepIndex] = useState(0);

  // Hook pour créer un SmartForm via l'API
  const createSmartFormMutation = useCreateSmartForm();

  const initialFormConfig: SmartFormConfig = {
    title: '',
    description: '',
    selectedSourceIds: [],
    targetEntityId: '',
    recipients: [],
    selectedFields: [],
    deadline: undefined,
    instructions: '',
  };

  const [formConfig, setFormConfig] = useState<SmartFormConfig>(initialFormConfig);
  const [entitySearchTerm, setEntitySearchTerm] = useState('');
  const [fieldsSearchTerm, setFieldsSearchTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generatedPublicLink, setGeneratedPublicLink] = useState('');
  const [isLoadingDraft, setIsLoadingDraft] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Variables et fonctions manquantes
  const titleInputRef = useRef<HTMLInputElement>(null);
  const wizardStepsDisplay = [
    { id: 'title-source' as WizardStep, name: 'Titre & Source' },
    { id: 'entity' as WizardStep, name: 'Entité Cible' },
    { id: 'fields' as WizardStep, name: 'Champs à Collecter' },
    { id: 'recipients' as WizardStep, name: 'Destinataires' },
    { id: 'review' as WizardStep, name: 'Révision et Validation' },
  ];

  // Restaurer le brouillon et l'état de navigation au montage
  useEffect(() => {
    try {
      const savedDraft = localStorage.getItem('smartFormDraft');
      if (savedDraft) {
        const draftConfig = JSON.parse(savedDraft);
        const safeConfig: SmartFormConfig = {
          ...initialFormConfig,
          ...draftConfig,
          selectedSourceIds: Array.isArray(draftConfig.selectedSourceIds) ? draftConfig.selectedSourceIds : [],
          selectedFields: Array.isArray(draftConfig.selectedFields) ? draftConfig.selectedFields : [],
          recipients: Array.isArray(draftConfig.recipients) ? draftConfig.recipients : [],
        };
        setFormConfig(safeConfig);
      }

      let restoredStep = 'title-source' as WizardStep;
      const savedCurrentStep = localStorage.getItem('smartFormCurrentStep');
      if (savedCurrentStep && wizardStepsDisplay.some(s => s.id === savedCurrentStep)) {
        restoredStep = savedCurrentStep as WizardStep;
      }
      setCurrentStep(restoredStep);

      const savedMaxIndex = localStorage.getItem('smartFormMaxReachedStepIndex');
      if (savedMaxIndex) {
        const parsedMaxIndex = parseInt(savedMaxIndex, 10);
        if (!isNaN(parsedMaxIndex) && parsedMaxIndex >= 0 && parsedMaxIndex < wizardStepsDisplay.length) {
          setMaxReachedStepIndex(parsedMaxIndex);
        } else {
          const restoredStepIndex = wizardStepsDisplay.findIndex(s => s.id === restoredStep);
          setMaxReachedStepIndex(Math.max(0, restoredStepIndex));
        }
      } else {
        const restoredStepIndex = wizardStepsDisplay.findIndex(s => s.id === restoredStep);
        setMaxReachedStepIndex(Math.max(0, restoredStepIndex));
      }

    } catch (error) {
      console.error("Erreur lors de la restauration du brouillon/état", error);
      localStorage.removeItem('smartFormDraft');
      localStorage.removeItem('smartFormCurrentStep');
      localStorage.removeItem('smartFormMaxReachedStepIndex');
      setCurrentStep('title-source');
      setMaxReachedStepIndex(0);
    }
    setIsLoadingDraft(false);
  }, []); // Exécuter une seule fois au montage

  // Sauvegarder le brouillon et l'état de navigation
  useEffect(() => {
    if (!isLoadingDraft) {
      try {
        localStorage.setItem('smartFormDraft', JSON.stringify(formConfig));
        localStorage.setItem('smartFormCurrentStep', currentStep);
        localStorage.setItem('smartFormMaxReachedStepIndex', maxReachedStepIndex.toString());
      } catch (error) {
        console.error("Erreur lors de la sauvegarde du brouillon/état", error);
      }
    }
  }, [formConfig, currentStep, maxReachedStepIndex, isLoadingDraft]);

  useEffect(() => {
    if (isLoadingDraft) return;

    const currentStepOrderIndex = wizardStepsDisplay.findIndex(s => s.id === currentStep);
    if (currentStepOrderIndex !== -1 && currentStepOrderIndex > maxReachedStepIndex) {
      setMaxReachedStepIndex(currentStepOrderIndex);
    }
  }, [currentStep, isLoadingDraft, maxReachedStepIndex]);

  const startNewForm = () => {
    setFormConfig(initialFormConfig);
    setCurrentStep('title-source');
    setEntitySearchTerm('');
    setFieldsSearchTerm('');
    setGeneratedPublicLink('');
    setIsSubmitting(false);
    setMaxReachedStepIndex(0);
    localStorage.removeItem('smartFormDraft');
    localStorage.removeItem('smartFormCurrentStep');
    localStorage.removeItem('smartFormMaxReachedStepIndex');
  };

  const handleNextStep = () => {
    const currentIndex = wizardStepsDisplay.findIndex(step => step.id === currentStep);
    if (currentStep === 'review') {
      handleSubmit();
    } else if (currentIndex !== -1 && currentIndex < wizardStepsDisplay.length - 1) {
      setCurrentStep(wizardStepsDisplay[currentIndex + 1].id);
    }
  };

  const handlePreviousStep = () => {
    const currentIndex = wizardStepsDisplay.findIndex(step => step.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(wizardStepsDisplay[currentIndex - 1].id);
    }
  };

  const handleSubmit = async () => {
    console.log('[wizard.tsx] handleSubmit: Function called');
    setIsSubmitting(true);
    setError(null);
    setGeneratedPublicLink('');

    try {
      // Simuler la création du formulaire
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockPublicFormId = `form_${Date.now()}`;
      // Utiliser la fonction utilitaire qui s'adapte automatiquement au port
      const mockGeneratedLink = generateFormUrl(mockPublicFormId);

      // Sauvegarder la configuration du formulaire dans localStorage pour que la route publique puisse la récupérer
      const allFieldsAvailable = getAllFieldsDetails();
      const publicFormConfig = {
        formId: mockPublicFormId,
        title: formConfig.title,
        description: formConfig.description,
        fields: formConfig.selectedFields.map(fieldId => {
          const fieldDetail = allFieldsAvailable.find(f => f.id === fieldId);
          if (!fieldDetail) return null;
          return {
            id: fieldDetail.id,
            name: fieldDetail.name,
            label: fieldDetail.name,
            type: fieldDetail.type,
            required: fieldDetail.required,
            options: fieldDetail.options,
            placeholder: fieldDetail.description,
            example: fieldDetail.example
          };
        }).filter(Boolean),
        deadline: formConfig.deadline,
        instructions: formConfig.instructions,
        status: 'editable'
      };

      // Sauvegarder dans localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`formConfig-${mockPublicFormId}`, JSON.stringify(publicFormConfig));
        console.log('Configuration du formulaire sauvegardée dans localStorage:', mockPublicFormId);
      }

      // Créer un SmartForm via l'API pour qu'il apparaisse dans "Mes SmartForms"
      const newSmartFormData = {
        id: mockPublicFormId, // Utiliser le même ID que le lien public
        title: formConfig.title, // L'API attend 'title', pas 'name'
        description: formConfig.description || '',
        fields: formConfig.selectedFields.map(fieldId => {
          const fieldDetail = allFieldsAvailable.find(f => f.id === fieldId);
          if (!fieldDetail) return null;
          return {
            id: fieldDetail.id,
            name: fieldDetail.name,
            type: fieldDetail.type as any,
            required: fieldDetail.required,
            options: fieldDetail.options,
            description: fieldDetail.description
          } as OdooField;
        }).filter(Boolean) as OdooField[]
      };

      // Créer via l'API (cela invalidera automatiquement le cache React Query)
      try {
        await createSmartFormMutation.mutateAsync(newSmartFormData);
        console.log('SmartForm créé via API:', mockPublicFormId);
      } catch (error) {
        console.error('Erreur lors de la création du SmartForm:', error);
        // En cas d'erreur, on continue quand même (le formulaire public fonctionne)
      }

      setFormConfig(prev => ({ ...prev, publicFormId: mockPublicFormId }));
      setGeneratedPublicLink(mockGeneratedLink);

      console.log('[wizard.tsx] handleSubmit: Form created successfully');
    } catch (error) {
      console.error('[wizard.tsx] handleSubmit: Error creating form', error);
      setError('Erreur lors de la création du formulaire. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCopyLink = (link: string) => {
    console.log('Lien copié (depuis wizard):', link);
  };

  const handlePreviewLink = (link: string) => {
    console.log('Lien prévisualisé (depuis wizard):', link);
  };

  const renderStepContent = () => {
    if (isLoadingDraft) {
      return (
        <Flex direction="column" align="center" justify="center" style={{ minHeight: '300px' }}>
          <Spinner size="3" />
          <Text mt="3">Chargement du brouillon...</Text>
        </Flex>
      );
    }

    switch (currentStep) {
      case 'title-source':
        return (
          <TitleSourceStep
            formConfig={formConfig}
            setFormConfig={setFormConfig}
            onNext={handleNextStep}
            titleInputRef={titleInputRef}
            sources={mockConnectedSources}
          />
        );
      case 'entity':
        return (
          <EntityStep
            formConfig={formConfig}
            setFormConfig={setFormConfig}
            entities={getAllAvailableEntities().filter(entity =>
              (formConfig.selectedSourceIds || []).length === 0 ||
              (formConfig.selectedSourceIds || []).includes(entity.sourceId)
            )}
            searchTerm={entitySearchTerm}
            setSearchTerm={setEntitySearchTerm}
            onNext={handleNextStep}
          />
        );
      case 'fields':
        const availableFields = getAllFieldsDetails().filter((field: FieldToCollect) =>
          (formConfig.selectedSourceIds || []).includes(field.sourceId)
        );
        return (
          <FieldsStep
            formConfig={formConfig}
            setFormConfig={setFormConfig}
            fields={availableFields}
            searchTerm={fieldsSearchTerm}
            setSearchTerm={setFieldsSearchTerm}
            templates={mockTemplates}
            onNext={handleNextStep}
          />
        );
      case 'recipients':
        return (
          <RecipientsStep
            formConfig={formConfig}
            setFormConfig={setFormConfig}
            onNext={handleNextStep}
          />
        );
      case 'review':
        return (
          <ReviewStep
            formConfig={formConfig}
            setFormConfig={setFormConfig}
            onSubmit={handleSubmit}
            onBack={handlePreviousStep}
            isSubmitting={isSubmitting}
            publicFormId={formConfig.publicFormId}
            generatedLink={generatedPublicLink}
            onCopyLinkProp={handleCopyLink}
            onPreviewLinkProp={handlePreviewLink}
            onNewForm={startNewForm}
          />
        );
      default:
        return <Text>Erreur: Étape non gérée ({currentStep})</Text>;
    }
  };

  if (isLoadingDraft) {
    return (
      <Flex justify="center" align="center" style={{ minHeight: 'calc(100vh - 200px)' }}>
        <Spinner size="3" />
        <Text ml="3">Chargement du brouillon...</Text>
      </Flex>
    );
  }

  if (error) {
    return (
      <Flex direction="column" align="center" justify="center" style={{ minHeight: 'calc(100vh - 200px)' }}>
        <Text color="red" size="4" mb="4">{error}</Text>
        <Button onClick={() => setError(null)}>Réessayer</Button>
      </Flex>
    );
  }

  return (
    <>
      <StepperNavigation
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        steps={wizardStepsDisplay}
        maxReachedStepIndex={maxReachedStepIndex}
      />

      <Box maxWidth="768px" mx="auto" width="100%" px="4">
        {renderStepContent()}
      </Box>
    </>
  );
}