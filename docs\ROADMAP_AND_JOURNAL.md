# Roadmap et Journal du projet Diktasis

## Roadmap

Voici les grandes étapes prévues pour le projet Diktasis et leur état d'avancement :

**Phase 1 : Initialisation et Configuration (Terminée)**
- [x] ~~Initialisation du projet Next.js (TypeScript, Tailwind CSS, ESLint, App Router, alias d'import).~~
- [x] ~~Création du dépôt Git, configuration du `.gitignore` et push initial sur GitHub.~~
- [x] ~~Ajout et versionnage de la documentation technique initiale (`ARCHITECTURE.md`, `DECISIONS.md`, `MCP_IMPLEMENTATION.md`, `GCP_SETUP.md`).~~
- [x] ~~Installation et configuration de Google Cloud SDK (`gcloud`).~~
- [x] ~~Création du projet GCP `diktasis-mvp` et association de la facturation.~~
- [x] ~~Activation des APIs Cloud Run, Cloud SQL, Firebase via gcloud et résolution des problèmes de droits IAM.~~
- [x] ~~Création et association du projet Firebase à `diktasis-mvp`.~~
- [x] ~~Activation de Firebase Authentication (via console Firebase).~~
- [x] ~~Installation du SDK Firebase (`npm install firebase`) dans le projet Next.js.~~
- [x] ~~Création du fichier de configuration Firebase (`src/firebaseConfig.ts`).~~

**Phase 2 : Intégration et Développement UI/UX (Terminée)**
- [x] ~~Intégration de Firebase Authentication dans l'application Next.js~~:
  - [x] ~~Création des pages/composants de connexion, inscription, déconnexion.~~
  - [x] ~~Gestion des états d'authentification utilisateur (React Context).~~
  - [x] ~~Protection des routes et gestion des redirections.~~
  - [ ] (Optionnel) Intégration de fournisseurs d'identité tiers (Google, etc.).
- [x] ~~**Refonte UI & Design System**~~:
  - [x] ~~Adoption de **Radix UI Themes** comme bibliothèque de composants principale.~~
  - [x] ~~Clarification de l'utilisation de **Tailwind CSS** en complément pour les styles spécifiques.~~
  - [x] ~~Mise à jour de la page des sources de données (`src/app/dashboard/data-sources/page.tsx`) avec Radix UI.~~
  - [x] ~~**Gestion des Logos d'Applications**: Intégration via CDN de Simple Icons.~~
  - [x] ~~Mise à jour de la documentation technique (`docs/ARCHITECTURE.md`, `docs/DECISIONS.md`) pour refléter ces choix.~~
- [x] ~~**Améliorations Générales Dashboard**~~:
  - [x] ~~Poursuite de l'uniformisation du style et de l'ergonomie sur d'autres pages.~~
- [x] ~~**Développement MVP SmartForms**~~:
  - [x] ~~Création d'un assistant de création de SmartForm étape par étape.~~
  - [x] ~~Interface client pour remplir les SmartForms.~~
  - [x] ~~Page de validation et gestion des réponses pour l'agence.~~
  - [x] ~~Simulation de synchronisation CRM avec workflow complet.~~
  - [x] ~~Workflow de bout en bout : Création → Envoi → Remplissage → Validation → Synchronisation.~~
    - [ ] Test du workflow et validation pour lancement MVP
- [x] ~~**Améliorations UX et Composants**~~:
  - [x] ~~Implémentation du format de date français (jj/mm/aaaa) dans toute l'application.~~
  - [x] ~~Création d'un composant DatePicker personnalisé avec Radix UI et react-day-picker.~~
  - [x] ~~Gestion des champs obligatoires personnalisés dans les SmartForms.~~
  - [x] ~~Correction des problèmes d'authentification et de navigation.~~

**Phase 3 : Développement des Fonctionnalités MVP (À venir)**
- [ ] Définition détaillée et priorisation des fonctionnalités MVP.
- [ ] Conception et configuration de la base de données (Cloud SQL ou Firestore) :
  - [ ] Choix final de la technologie de base de données si pas encore fait.
  - [ ] Définition du schéma de la base de données.
  - [ ] Implémentation des modèles et des interfaces de données.
- [ ] Développement des fonctionnalités clés :
  - [ ] Dashboard principal : Vue d'ensemble, widgets clés.
  - [ ] Gestion avancée des sources de données :
    - [ ] Implémentation du catalogue d'applications connectables.
    - [ ] Logique de distinction et d'affichage des sources connectées / non connectées.
    - [ ] Fonctionnalité d'ajout/configuration de sources de données (y compris personnalisées).
    - [ ] Opérations CRUD de base pour les sources configurées.
  - [ ] ... (Autres fonctionnalités MVP à définir / détailler)

**Phase 4 : Tests, Déploiement et Itérations (À venir)**
- [ ] Mise en place d'une stratégie de tests :
  - [ ] Tests unitaires (Jest, React Testing Library).
  - [ ] Tests d'intégration.
  - [ ] (Optionnel) Tests End-to-End (Cypress, Playwright).
- [ ] Configuration du pipeline CI/CD pour le déploiement.
- [ ] Déploiement initial sur Cloud Run.
- [ ] Phase de recette interne et collecte de feedback.
- [ ] Documentation utilisateur.
- [ ] Itérations et améliorations basées sur les retours.

**Phase 5 : Lancement et Présence en Ligne (À venir)**
*   [ ] **Définition de la Stratégie du Site Web de Présentation**
    *   [x] ~~Création du document de stratégie (`PRESENTATION_SITE.md`)~~ (Fait le YYYY-MM-DD)
    *   [ ] Validation des objectifs, public cible, et fonctionnalités clés.
    *   [ ] **Choix final de la stack technique** (Headless CMS, SSG/Framework, Hébergement, Auth, etc. - voir `PRESENTATION_SITE.md`)
    *   [ ] Documentation du choix et de la justification dans `PRESENTATION_SITE.md` et `DECISIONS.md`.
*   [ ] **Développement du Site Web de Présentation (Vitrine)**
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Conception et maquettage du site (UI/UX) - en s'inspirant d'Attio.com
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Initialisation du projet Next.js pour le site vitrine (installation, configuration Radix UI, Tailwind CSS).
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Mise en place de Strapi sur GCP (Cloud Run + Cloud SQL) et configuration initiale.
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Développement du socle technique (initialisation projet selon stack choisie).
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Mise en place du CMS et configuration initiale du contenu.
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Développement des pages principales :
        *   [ ] Page d'accueil (Proposition de valeur, fonctionnalités clés, appel à l'action)
        *   [ ] Page "Fonctionnalités" (Détail des capacités de Diktasis, avec visuels techniques)
        *   [ ] Page "Tarifs" (Si applicable, présentation des différentes offres)
        *   [ ] Blog (avec gestion des articles, catégories, tags)
        *   [ ] Système de compte utilisateur (Inscription, Connexion, Espace basique)
        *   [ ] Page "À Propos" (Mission, vision)
        *   [ ] Page "Contact" (Formulaire de contact, informations de support)
        *   [ ] Pages légales (Mentions légales, Politique de confidentialité, CGU)
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Intégration de la génération/gestion d'images techniques (screenshots, tables, etc.)
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Optimisation SEO (technique et contenu).
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Intégration d'un outil d'analyse d'audience (ex: Google Analytics, Plausible).
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Tests et recettes.
    *   [ ] **(À FAIRE ULTÉRIEUREMENT)** Mise en ligne du site.
*   [ ] **(À FAIRE ULTÉRIEUREMENT)** Stratégie de contenu initiale pour le blog (premiers articles).
*   [ ] **(À FAIRE ULTÉRIEUREMENT)** Préparation des canaux de communication (réseaux sociaux, etc.).

## Journal d'évolution

## 2024-05-22

- Initialisation du projet Next.js avec TypeScript, Tailwind CSS, ESLint, App Router, et configuration des alias d'import.
- Création du dépôt Git, ajout du .gitignore, premier commit et sauvegarde du projet sur GitHub.
- Ajout et versionnage de la documentation technique (ARCHITECTURE.md, DECISIONS.md, MCP_IMPLEMENTATION.md).
- Création du fichier `GCP_SETUP.md` pour documenter la configuration de Google Cloud Platform (GCP).
- Installation et configuration de l'outil en ligne de commande Google Cloud SDK (`gcloud`).
- Création du projet GCP `diktasis-mvp` et association de la facturation.
- Activation des APIs Cloud Run, Cloud SQL, Firebase via gcloud.
- Résolution de problèmes de droits d'activation d'API (Firebase Auth) et vérification des rôles IAM.
- Création et association du projet Firebase à `diktasis-mvp`.
- Documentation des étapes et vérification de la bonne association entre Firebase et GCP.
- Installation du SDK Firebase (`npm install firebase`) dans le projet Next.js pour préparer l'intégration de l'authentification et des services Firebase.
- Création du fichier `src/firebaseConfig.ts` avec la configuration du projet Firebase (clés API, projectId, etc.), prêt à être utilisé dans l'application Next.js pour l'authentification et les autres services Firebase.

## 2024-05-23

- Lecture approfondie des documents d'architecture, de décisions techniques et d'implémentation MCP dans le dossier `docs`.
- Résolution de conflits de fichiers lors de l'initialisation du projet Next.js, validation du bon fonctionnement local.
- Ajout du dépôt distant GitHub, push initial, et adaptation du `.gitignore`.
- Création et configuration du projet GCP `diktasis-mvp` via gcloud, association de la facturation, activation des APIs nécessaires (Cloud Run, Cloud SQL, Firebase).
- Diagnostic et résolution des problèmes de droits IAM pour l'activation de certaines APIs, notamment Firebase Auth (passage par la console Firebase, vérification des rôles et de l'association des projets).
- Création et association du projet Firebase au projet GCP existant, activation de l'authentification dans la console Firebase.
- Mise en place d'un suivi détaillé dans ce journal, avec des entrées horodatées et un résumé des grandes étapes (initialisation, Git, GCP, Firebase, résolution de problèmes, etc.).
- Prochaines étapes : intégration de Firebase Auth dans l'application Next.js, configuration de la base de données, développement des fonctionnalités MVP, et poursuite de la documentation.

Résumé détaillé :
Le projet Diktasis a été initialisé, versionné, documenté, le cloud et Firebase sont configurés et associés, les APIs principales sont activées, et un journal d'évolution a été mis en place pour suivre l'avancement. Les prochaines étapes concernent l'intégration technique des services et le développement fonctionnel.

## 2024-05-24

- **Refonte UI & Design System**:
  - Adoption de **Radix UI Themes** comme bibliothèque de composants principale pour le frontend.
  - Clarification de l'utilisation de **Tailwind CSS** en complément de Radix UI pour les styles spécifiques.
  - Mise à jour de la page des sources de données (`src/app/dashboard/data-sources/page.tsx`) pour utiliser Radix UI (Box, Flex, Card, Avatar, Grid, etc.) et supprimer les classes Tailwind redondantes.
  - **Gestion des Logos d'Applications**: Intégration des logos des sources de données via des URL CDN de [Simple Icons](https://simpleicons.org/), avec spécification des couleurs de marque. Cela élimine le besoin de stocker les SVG localement.
  - Mise à jour de la documentation technique (`docs/ARCHITECTURE.md`, `docs/DECISIONS.md`) pour refléter ces choix.
- **Améliorations Générales Dashboard**:
  - Poursuite de l'uniformisation du style et de l'ergonomie sur d'autres pages du dashboard (implicite basé sur l'historique de la conversation).

## 2024-05-25

- **Développement MVP SmartForms - Workflow Complet**:
  - **Assistant de Création**: Développement d'un wizard étape par étape pour créer des SmartForms (`src/app/dashboard/new-smart-forms/wizard.tsx`) avec :
    - Configuration du formulaire (titre, description, client, deadline)
    - Sélection intelligente des champs par groupes (Général, Contact, Comptabilité, Fiscalité, Bancaire)
    - Gestion des destinataires (contacts CRM + emails manuels)
    - Prévisualisation et finalisation
  - **Interface Client**: Création d'une interface dédiée pour les clients (`src/app/form/[id]/page.tsx`) avec :
    - Affichage responsive et intuitif
    - Validation en temps réel des champs obligatoires
    - Indicateur de progression
    - Gestion des différents types de champs (text, email, select, textarea, date)
  - **Page de Validation Agence**: Interface pour valider les réponses (`src/app/dashboard/smart-forms/[id]/page.tsx`) avec :
    - Visualisation des modifications apportées par le client
    - Comparaison ancien/nouveau pour les champs modifiés
    - Actions de validation/rejet avec notes
    - Statuts visuels et badges
  - **Simulation Synchronisation CRM**: Workflow de synchronisation (`src/app/dashboard/smart-forms/[id]/sync/page.tsx`) avec :
    - Étapes détaillées de synchronisation (validation, mapping, mise à jour contact/entreprise, documents, notifications)
    - Progression en temps réel avec animations
    - Simulation réaliste avec durées et détails d'exécution
  - **Workflow Complet**: Implémentation du parcours de bout en bout :
    1. Création du SmartForm par l'agence
    2. Génération de lien et envoi au client
    3. Remplissage par le client avec validation
    4. Validation par l'agence avec gestion des modifications
    5. Synchronisation CRM avec suivi détaillé

## 2024-12-19

- **Amélioration du DatePicker avec Format Français**:
  - **Création d'un composant DatePicker personnalisé** (`src/components/DatePicker.tsx`) utilisant Radix UI Popover et react-day-picker
  - **Format de date français** : Implémentation du format jj/mm/aaaa avec validation automatique de saisie
  - **Interface utilisateur améliorée** :
    - Bouton "Choisir une date" visible et accessible à côté du champ de saisie
    - Champ prérempli avec le format attendu ("jj/mm/aaaa") pour faciliter la saisie manuelle
    - Fenêtre de calendrier carrée et compacte (320px de largeur)
  - **Mise en évidence de la date du jour** : Fond coloré avec bordure bleue pour identifier facilement la date actuelle
  - **Gestion des états** : Style spécial quand la date du jour est également sélectionnée
  - **Intégration dans le wizard SmartForms** : Remplacement du champ de date limite par le nouveau DatePicker
  - **Dépendances ajoutées** : react-day-picker et date-fns pour la gestion des dates et la localisation française
  - **Styles CSS personnalisés** (`src/components/DatePicker.module.css`) avec variables Radix UI pour une cohérence visuelle

## 2024-05-26

- **Amélioration de l'Assistant de Création de SmartForm (`wizard.tsx`)**:
  - **Refonte de l'indicateur d'étapes**:
    - Affichage permanent des numéros sur chaque étape.
    - Nouveau schéma de couleurs : bleu pâle (fond `var(--blue-4)`, texte `var(--blue-11)`) pour les étapes complétées, bleu vif (fond `var(--accent-9)`, texte blanc) pour l'étape en cours, et gris (fond `var(--gray-a3)`, texte `var(--gray-a11)`) pour les étapes non réalisées.
    - Ajout de la navigation par clic sur les indicateurs d'étapes, permettant de revenir aux étapes précédentes ou d'avancer si les étapes intermédiaires sont valides.
  - **Implémentation de la sauvegarde automatique en brouillon**:
    - L'état du formulaire (configuration et étape actuelle) est sauvegardé dans le `localStorage` du navigateur à chaque modification ou changement d'étape.
  - **Fonctionnalité de reprise de brouillon**:
    - Au chargement de la page de création, si un brouillon est détecté dans `localStorage`, une boîte de dialogue propose à l'utilisateur de reprendre le brouillon existant ou de commencer un nouveau formulaire.
    - Le brouillon est effacé du `localStorage` si l'utilisateur opte pour un nouveau formulaire ou après la soumission du SmartForm.
    - **Objectif**: Augmenter la convivialité et la robustesse du processus de création de SmartForms, en évitant la perte de données et en offrant plus de flexibilité.

## 2024-05-27

- **Amélioration du Composant `RecipientTagInput.tsx` (utilisé dans l'étape 1 du wizard SmartForm)**:
  - **Auto-complétion des contacts CRM améliorée**:
    - Les suggestions de contacts CRM (issues de `mockContacts` et filtrées par l'entreprise sélectionnée dans le wizard) apparaissent désormais dès la saisie du premier caractère dans le champ de destinataires.
    - La liste déroulante des suggestions ne s'affiche que si des contacts pertinents sont trouvés.
  - **Édition des pastilles d'emails manuels**:
    - Les pastilles représentant des emails saisis manuellement sont maintenant cliquables pour édition.
    - Un clic sur le texte de la pastille la transforme en champ de saisie (`TextField`) pré-rempli, permettant la modification de l'adresse email.
    - La sauvegarde s'effectue à la perte de focus (`onBlur`) ou en appuyant sur "Entrée". La touche "Escape" annule l'édition.
    - Des validations sont en place pour les emails modifiés (format valide, non-duplication avec d'autres destinataires).
    - Si le champ d'édition est vidé, la pastille est supprimée.
  - **Comportement général affiné**:
    - Le focus est géré pour passer intuitivement au champ d'édition et revenir à l'input principal.
    - L'état d'édition est coordonné avec les autres actions du composant (ajout de nouvelles pastilles, collage, auto-complétion).
  - **Objectif**: Rendre la saisie des destinataires plus fluide, intuitive et flexible, se rapprochant de l'expérience utilisateur de clients mail modernes comme Gmail.

## 2024-12-20

- **Refonte Majeure du SmartForm Wizard - Style Tally.so**:
  - **Amélioration du style et de l'ergonomie**:
    - Modification du placeholder du titre de "Nom de votre formulaire d'enrichissement..." vers "Nom de votre SmartForm..."
    - Réduction significative des espaces entre sections pour éviter le scroll excessif (gap principal de 8 à 5, espacements étapes de 20px à 12px, marge description de 40px à 16px)
    - Optimisation du padding du container principal de `var(--space-8)` à `var(--space-4)` pour améliorer la visibilité dans la ligne de flottaison
    - Ajout de `paddingTop: '80px'` puis `paddingBottom: '300px'` pour centrer le contenu et donner plus d'espace pour les dropdowns
  - **Amélioration typographique du titre**:
    - Style très bold et condensé pour le nom du formulaire : `fontSize: '36px'`, `fontWeight: '900'`, `fontStretch: 'condensed'`, `letterSpacing: '-0.02em'`
  - **Évolution des modèles prédéfinis de sélection de champs**:
    - Remplacement des anciens modèles (🎯 Champs essentiels, 📝 Enrichissement complet, 👥 Informations de contact, 💰 Données financières)
    - Nouveaux modèles métier : 🧾 Coordonnées de facturation, 💼 Données LinkedIn, 🎨 Branding, 💰 Données financières (modifié), 📢 Google Ads, 📘 Création de page Facebook
    - Chaque modèle utilise des filtres spécifiques basés sur des mots-clés dans les noms de champs
  - **Ajout de boutons pour sources de données**:
    - Bouton en en-tête avec icône PlusCircledIcon et texte "Ajouter une source"
    - Redirection vers `/dashboard/data-sources` via `useRouter` de Next.js
  - **Résolution du problème de scalabilité des champs**:
    - **Interface principale simplifiée** : Remplacement de la liste complexe par un résumé compact
    - **État vide** : Card avec icône MagicWandIcon et invitation à sélectionner
    - **État rempli** : Aperçu des 4 premiers champs avec bouton "Modifier"
  - **Transformation en flux linéaire style Tally**:
    - **Suppression complète de la modal** : Élimination du `Dialog.Root` de sélection des champs
    - **Variable d'état** : Ajout de `isInFieldSelection` pour gérer la navigation entre étapes
    - **Bouton de transition** : "Choisir les champs →" positionné en bas à droite de l'interface principale
    - **Interface de sélection intégrée** avec :
      - Bouton retour "← Retour" pour revenir à l'interface principale
      - Titre et description clairs pour l'étape de sélection
      - Barre d'outils avec compteur de sélection et actions globales (Tout sélectionner/désélectionner)
      - Modèles prédéfinis conservés mais dans l'interface dédiée
      - Recherche en temps réel avec TextField.Root et MagnifyingGlassIcon
      - Filtres rapides : Tous / Requis / À compléter
      - Zone scrollable avec organisation par priorité :
        - ⚠️ Champs requis en premier
        - Par source avec groupement par statut (🔴 vides, 🟡 partiels, 🟢 complets)
      - Boutons de confirmation avec comptage dynamique ("Continuer avec X champs sélectionnés")
  - **Corrections techniques**:
    - Résolution du problème de double scrollbar en supprimant `minHeight: '100vh'` du container principal et `overflowY: 'auto'` dans DashboardLayout.tsx
    - Optimisation de l'espace pour les dropdowns avec `paddingBottom: '300px'`
  - **Objectif** : Créer une expérience utilisateur fluide et moderne inspirée de Tally.so, avec un flux linéaire intuitif qui élimine les modals au profit d'une navigation séquentielle plus naturelle.

## 2024-12-21

- **Refonte Complète de la Dernière Étape du Wizard SmartForm**:
  - **Design moderne et épuré** : Remplacement de l'ancienne étape par un design centré et professionnel
  - **Icône de succès** : Ajout d'une icône CheckCircledIcon dans un cercle vert de 80px pour marquer la validation
  - **Titre et description améliorés** : "SmartForm validé !" avec description contextualisée incluant le nom du formulaire
  - **Carte informative** : Résumé des informations clés (champs, destinataires, sources) avec badges colorés
  - **Lien de partage avec copie** : 
    - Affichage du lien dans une carte avec fond vert et police monospace
    - Bouton "Copier" avec icône qui devient "Copié !" avec animation temporaire
    - Message de confirmation "Lien copié !" en popover temporaire (2 secondes)
  - **Actions principales** : Boutons "Aperçu" et "Envoyer par Email" avec icônes et styles distincts
  - **Informations contextuelles** : Affichage des destinataires avec noms et comptage intelligent
  - **Actions secondaires** : Liens discrets vers détails et nouveau SmartForm
  - **Changement du bouton de validation** : "Envoyer le formulaire" → "Valider" dans l'étape ReviewStep

- **Implémentation de la Multi-Sélection de Modèles**:
  - **Remplacement des boutons par Select multi-sélection** : Utilisation de Radix UI Select avec possibilité de choisir plusieurs modèles
  - **Logique de combinaison intelligente** : Les champs de tous les modèles sélectionnés sont automatiquement combinés sans doublons
  - **Affichage des modèles sélectionnés** : Pastilles (Badge) avec boutons de suppression individuelle et option "Effacer tout"
  - **Interface intuitive** : Description claire expliquant la possibilité de combiner plusieurs modèles
  - **8 modèles disponibles** alignés sur la page Templates :
    - 🧾 Coordonnées de facturation (8 champs)
    - 👤 Informations contact principal (6 champs)  
    - 💼 Qualification commerciale (12 champs)
    - 🔗 Page LinkedIn Entreprise (10 champs)
    - 📢 Campagne Marketing (9 champs)
    - 🚀 Onboarding Client (15 champs)
    - ⚙️ Informations techniques (11 champs)
    - 🛡️ Conformité RGPD (7 champs)

- **Vérification et Validation des Statuts**:
  - **Page Mes SmartForms** : Vérification que les statuts sont déjà correctement traduits en français
  - **Composants SmartFormsFilters et SmartFormsTable** : Confirmation que les traductions ("Brouillon", "Envoyé", "Complété") sont déjà implémentées
  - **Données mockées** : Validation que le statut "draft" est inclus dans les types et données de test
  - **Types TypeScript** : Confirmation que `SmartFormStatus` inclut 'draft' | 'sent' | 'completed'

- **Amélioration de l'Étape Destinataires** :
  - **Titre dynamique selon le type d'entité** : Personnalisation pour contacts vs entreprises
  - **Description contextuelle adaptée** : Messages différents selon le contexte
  - **Pré-remplissage automatique** : Email du contact principal ajouté automatiquement si un contact est sélectionné
  - **Carte de rappel de l'entité** : Affichage contextualisé de l'entité sélectionnée avec avatar et détails
  - **Email de test ajouté** : <EMAIL> pour les démos

- **Optimisations techniques** :
  - **Suppression de fonctions obsolètes** : Nettoyage de l'ancienne logique de templates après migration vers multi-sélection
  - **Imports nettoyés** : Ajout de Select dans les imports Radix UI
  - **Logique simplifiée** : Réduction de la complexité du code avec la nouvelle approche multi-sélection

---

Ce journal retrace les grandes étapes techniques et administratives du projet Diktasis, incluant la mise en place de l'environnement de développement, la gestion du code source, la configuration du cloud, l'intégration de Firebase et la résolution des principaux obstacles rencontrés.

Chaque nouvelle action ou décision importante pourra être ajoutée ici, avec la date, pour garder un historique clair et partagé de l'évolution du projet. 