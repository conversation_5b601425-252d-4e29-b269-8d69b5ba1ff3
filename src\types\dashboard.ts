export type FormStatus = 'draft' | 'sent' | 'pending' | 'submitted' | 'archived' | 'error' | 'completed';
export type FieldCategory = 'identity' | 'contact' | 'address' | 'financial' | 'legal' | 'project' | 'custom';
export type OdooFieldType = 'string' | 'number' | 'date' | 'boolean' | 'select' | 'textarea';

export interface OdooFieldOption {
  id: string | number;
  name: string;
}

export interface OdooField {
  id: string;
  name: string;
  label?: string;
  type: OdooFieldType;
  required: boolean;
  category: FieldCategory;
  description?: string;
  placeholder?: string;
  example?: string;
  options?: OdooFieldOption[];
}

export interface SmartForm {
  id: string;
  name: string;
  description: string;
  status: FormStatus;
  fields: OdooField[];
  createdAt: Date;
  updatedAt: Date;
  responses?: SmartFormResponse[];
  instructions?: string;
  deadline?: string;
  createdBy?: UserProfileShort;
  targetEntity?: {
    id: string;
    name: string;
    type: 'company' | 'contact';
  };
  template: boolean;
  version?: number;
  tags?: string[];
}

export interface SmartFormResponse {
  id: string;
  formId: string;
  status: 'pending' | 'completed' | 'expired' | 'processed' | 'sent_to_sources';
  submittedAt?: Date;
  data: Record<string, any>;
  clientInfo: {
    email: string;
    name: string;
  };
}

export interface DashboardStats {
  totalForms: number;
  activeForms: number;
  completedForms: number;
  responseRate: number;
  averageResponseTime: number;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  agency?: {
    id: string;
    name: string;
  };
  odooConnected: boolean;
  lastSync?: Date;
}

export interface UserProfileShort {
  id: string;
  name: string;
} 