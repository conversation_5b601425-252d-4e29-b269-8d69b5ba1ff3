export interface FieldOption {
  value: string;
  label: string;
}

export type FormFieldType = 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox' | 'radio';

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  example?: string;
  description?: string;
  options?: FieldOption[] | string[]; // Peut être des objets ou de simples strings
  rows?: number; // Pour textarea
  defaultValue?: any;
  readOnly?: boolean;
  min?: number;
  max?: number;
}

export interface FormConfig {
  formId: string;
  title: string;
  description?: string;
  fields: FormField[];
  submitUrl: string; // URL vers laquelle le formulaire sera soumis
  status: 'editable' | 'submitting' | 'submitted' | 'error' | 'preview';
  submitButtonText?: string;
  successMessage?: string;
  errorMessage?: string;
  deadline?: string;
  instructions?: string;
  // Autres configurations spécifiques au formulaire public
} 