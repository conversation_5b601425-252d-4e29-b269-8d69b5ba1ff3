import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>ton, Card, Flex, Heading, Text } from '@radix-ui/themes';
import { CheckCircledIcon, PlusIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';

export const ConnectedSourcesOverview: React.FC = () => {
  const router = useRouter();
  
  // Données mock des sources connectées (basées sur les données de data-sources)
  const connectedSources = [
    { name: 'Odoo', type: 'ERP/CRM', color: 'green' as const },
    { name: 'QuickBooks', type: 'Facturation', color: 'green' as const }
  ];

  return (
    <Card size="3">
      <Flex direction="column" gap="4">
        <Flex justify="between" align="center">
          <Heading as="h2" size="5" weight="bold">
            Sources Connectées
          </Heading>
          <Text size="2" color="gray">
            {connectedSources.length} active{connectedSources.length > 1 ? 's' : ''}
          </Text>
        </Flex>
        
        {connectedSources.length === 0 ? (
          <Box>
            <Text size="2" color="gray" mb="3" as="div">
              Aucune source connectée pour le moment
            </Text>
            <Button 
              size="2" 
              variant="soft" 
              onClick={() => router.push('/dashboard/data-sources')}
            >
              <PlusIcon width={16} height={16} />
              Connecter une source
            </Button>
          </Box>
        ) : (
          <Flex direction="column" gap="3">
            {connectedSources.map((source, index) => (
              <Flex key={index} align="center" justify="between">
                <Box>
                  <Flex align="center" gap="2" mb="1">
                    <CheckCircledIcon color="var(--green-11)" width={14} height={14} />
                    <Text weight="medium" size="2">{source.name}</Text>
                  </Flex>
                  <Text size="1" color="gray" as="div">{source.type}</Text>
                </Box>
                <Badge color={source.color} variant="soft" size="1">
                  Actif
                </Badge>
              </Flex>
            ))}
            
            <Button 
              size="2" 
              variant="ghost" 
              onClick={() => router.push('/dashboard/data-sources')}
              style={{ marginTop: 'var(--space-2)' }}
            >
              <PlusIcon width={16} height={16} />
              Ajouter une source
            </Button>
          </Flex>
        )}
      </Flex>
    </Card>
  );
}; 