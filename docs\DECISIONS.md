# Décisions Techniques

Ce document liste les décisions techniques importantes prises pour le projet, avec leur justification.

## Stack Technique

### Next.js 14
**Décision**: Utiliser Next.js 14 comme framework principal
**Justification**:
- Performance optimale avec le App Router
- API Routes intégrées pour le MVP
- Facilité de déploiement sur Vercel
- Support TypeScript natif
- SEO optimisé

### Apideck
**Décision**: Utiliser Apideck pour l'intégration CRM
**Justification**:
- API unifiée pour tous les CRM majeurs
- Réduction significative du temps de développement
- Gestion sécurisée des credentials via Vault
- SDK React disponible
- Support des CRM prioritaires (Salesforce, Hubspot, Odoo, etc.)

### GCP
**Décision**: Utiliser Google Cloud Platform
**Justification**:
- Intégration native avec Google Workspace
- Facturation à l'usage (économique pour le MVP)
- Services serverless (Cloud Run)
- Scalabilité automatique
- Services nécessaires disponibles (Cloud SQL, Storage)

### N8N Cloud
**Décision**: Utiliser N8N Cloud plutôt que self-hosted pour le MVP
**Justification**:
- Réduction de la complexité d'infrastructure
- Maintenance gérée
- Coût prévisible
- Migration possible vers self-hosted plus tard

## Architecture

### API Routes vs NestJS
**Décision**: Commencer avec API Routes, migration possible vers NestJS
**Justification**:
- Simplicité pour le MVP
- Pas de surcharge initiale
- Migration possible si besoin de plus de complexité
- Meilleure séparation des responsabilités avec NestJS

### PostgreSQL
**Décision**: Utiliser PostgreSQL comme base principale
**Justification**:
- Support des données structurées
- Scalabilité
- Intégration facile avec GCP
- Support des transactions

## Sécurité

### NextAuth.js
**Décision**: Utiliser NextAuth.js pour l'authentification
**Justification**:
- Support OAuth natif
- Sécurité éprouvée
- Facilité d'intégration
- Gestion des sessions

### Vault
**Décision**: Utiliser Vault d'Apideck pour les credentials
**Justification**:
- Gestion sécurisée des tokens
- Pas de stockage local des credentials
- Conformité RGPD
- Audit des accès

## Performance

### React Query
**Décision**: Utiliser React Query pour la gestion d'état
**Justification**:
- Gestion automatique du cache
- Optimistic updates
- Gestion des états de chargement
- Réduction des requêtes inutiles

## UI & Design System

### Radix UI Themes & Tailwind CSS
**Décision**: Adopter Radix UI Themes comme bibliothèque de composants principale, complétée par Tailwind CSS pour les ajustements.
**Justification**:
- **Radix UI Themes**:
  - Fournit un ensemble de composants UI accessibles, stylisés et prêts à l'emploi.
  - Offre un système de theming cohérent (tokens de design pour couleurs, espacements, typographie, radius) qui facilite la création d'une interface utilisateur homogène.
  - Conçu pour l'accessibilité (WAI-ARIA).
  - Simplifie la maintenance et l'évolution du design system.
- **Tailwind CSS**:
  - Permet des ajustements de style granulaires et la création de mises en page personnalisées qui ne seraient pas directement couvertes par les props des composants Radix.
  - Utilisé en complément, et non en remplacement, des styles de Radix Themes. La priorité est donnée aux tokens et props Radix pour maintenir la cohérence.
- **Stratégie d'intégration**:
  - Initialement, l'utilisation d'un preset Tailwind pour Radix (`@radix-ui/themes-preset-tailwind`) a été explorée mais s'est avérée non existante ou moins directe que l'utilisation séparée et complémentaire.
  - L'approche actuelle favorise l'utilisation des composants Radix avec leurs props de style. Les classes Tailwind sont ajoutées lorsque nécessaire, en veillant à ne pas créer de conflits stylistiques majeurs (par exemple, en surchargeant les styles fondamentaux des composants Radix).
  - La règle CSS globale `cursor: pointer !important;` a été ajoutée pour les boutons Radix (`.rt-Button-root`) pour assurer une expérience utilisateur cohérente, car ce n'était pas le comportement par défaut sur tous les variants.

### Gestion des Logos d'Applications (via CDN)
**Décision**: Utiliser des URLs CDN (Simple Icons) pour les logos des applications externes.
**Justification**:
- Évite de stocker et de maintenir des fichiers SVG dans le dépôt.
- Assure la cohérence et l'accès à des versions à jour des logos.
- Simplifie l'intégration : une simple URL suffit (ex: `https://cdn.simpleicons.org/salesforce/00A1E0`).
- Charge potentiellement plus rapide des images via CDN.

## Évolutivité

### Architecture Modulaire
**Décision**: Adopter une architecture modulaire
**Justification**:
- Facilité d'ajout de nouvelles fonctionnalités
- Séparation claire des responsabilités
- Maintenance simplifiée
- Tests plus faciles

### Cloud Native
**Décision**: Adopter une approche cloud-native
**Justification**:
- Scalabilité automatique
- Coûts optimisés
- Maintenance réduite
- Haute disponibilité

### MCP (Model Context Protocol)
**Décision**: Adopter le MCP pour l'intégration IA
**Justification**:
- Amélioration de l'assistance au développement
- Meilleure contextualisation des agents IA
- Sécurité renforcée via l'analyse contextuelle
- Maintenance simplifiée des composants IA
- Intégration native avec les outils de développement

### DatePicker avec react-day-picker
**Décision**: Créer un composant DatePicker personnalisé utilisant react-day-picker et date-fns
**Justification**:
- **Format français natif**: Support du format jj/mm/aaaa avec validation automatique
- **Intégration Radix UI**: Utilisation de Radix UI Popover pour une cohérence visuelle avec le design system
- **Localisation**: Support complet du français via date-fns/locale
- **Accessibilité**: react-day-picker respecte les standards WCAG
- **Flexibilité**: Composant réutilisable avec props configurables (size, placeholder, disabled)
- **UX optimisée**: 
  - Bouton "Choisir une date" visible et explicite
  - Champ prérempli avec le format attendu
  - Mise en évidence de la date du jour
  - Fenêtre de calendrier compacte et carrée
- **Maintenance**: Bibliothèques maintenues activement avec une large communauté 

### Assistant de Création de SmartForm (Wizard)

**Décision**: Améliorer l'expérience utilisateur de l'assistant de création de SmartForm.
**Justification**:
- **Navigation par étapes améliorée**:
  - Les numéros d'étapes sont toujours visibles.
  - Un code couleur distinctif est utilisé pour les étapes complétées (bleu pâle), l'étape active (bleu vif) et les étapes futures (gris).
  - Les indicateurs d'étapes sont cliquables, permettant une navigation directe vers les étapes précédentes ou les étapes suivantes si les étapes intermédiaires sont valides.
- **Sauvegarde automatique en brouillon**:
  - L'état actuel du formulaire (données saisies et étape en cours) est sauvegardé automatiquement dans le `localStorage` du navigateur de l'utilisateur.
  - Cela prévient la perte de données en cas de fermeture accidentelle de l'onglet ou du navigateur.
- **Reprise de brouillon**:
  - Au chargement de la page de création d'un nouveau SmartForm, si un brouillon est détecté dans le `localStorage`, l'utilisateur se voit proposer de reprendre sa session précédente ou de commencer un nouveau formulaire.
  - Le brouillon est supprimé du `localStorage` si l'utilisateur choisit de démarrer un nouveau formulaire ou après la soumission réussie du SmartForm.
- **Objectif**: Fluidifier le processus de création, réduire la frustration liée à la perte de données et permettre une plus grande flexibilité dans la complétion des formulaires longs.

### SmartForm Wizard - Flux Linéaire Style Tally.so

**Décision**: Transformer l'interface de sélection des champs d'une modal vers un flux linéaire intégré.
**Justification**:
- **Inspiration Tally.so**: Adoption du modèle de navigation séquentielle de Tally.so, reconnu pour son excellence UX dans la création de formulaires
- **Élimination des modals**:
  - Les modals créent une rupture dans l'expérience utilisateur
  - Le flux linéaire offre une progression plus naturelle et intuitive
  - Meilleure gestion de l'espace d'affichage, particulièrement sur mobile
- **Gestion d'état simplifiée**:
  - Variable `isInFieldSelection` pour gérer la navigation entre l'interface principale et la sélection des champs
  - État plus prévisible et debuggable
  - Pas de gestion complexe d'ouverture/fermeture de modal
- **Amélioration de l'accessibilité**:
  - Navigation au clavier plus fluide
  - Pas de piège de focus lié aux modals
  - Meilleure compatibilité avec les lecteurs d'écran
- **Scalabilité**:
  - Interface principale simplifiée avec résumé compact des champs sélectionnés
  - Interface dédiée pour la sélection permettant de gérer des centaines de champs
  - Recherche et filtres intégrés pour une navigation efficace
- **Cohérence visuelle**:
  - Utilisation cohérente des composants Radix UI
  - Transitions fluides entre les étapes
  - Maintien du contexte visuel (titre, progression)

**Implémentation technique**:
- Suppression du `Dialog.Root` et de ses composants associés
- Rendu conditionnel basé sur `isInFieldSelection`
- Conservation de toutes les fonctionnalités (recherche, filtres, modèles prédéfinis)
- Boutons de navigation explicites ("Choisir les champs →", "← Retour")
- Comptage dynamique des champs sélectionnés dans les boutons d'action

**Résultats attendus**:
- Réduction du taux d'abandon lors de la création de SmartForms
- Amélioration de la satisfaction utilisateur
- Interface plus moderne et alignée sur les standards actuels
- Meilleure performance (pas de gestion de z-index et de portails)

## Site Web de Présentation (Vitrine)

### Stack Technique du Site Vitrine
**Décision**: Adopter une stack moderne et performante pour le site de présentation, inspirée par les meilleures pratiques et les contraintes budgétaires.
**Stack Choisie**:
- **Framework Frontend**: Next.js (React)
- **Hébergement Frontend**: Vercel (Plan gratuit/personnel pour commencer)
- **CMS Headless**: Strapi (Open Source), auto-hébergé sur GCP (Cloud Run pour l'application Strapi, Cloud SQL PostgreSQL pour la base de données).
- **Authentification (Comptes Utilisateurs Site)**: Firebase Authentication
- **Bibliothèque de Composants UI**: Radix UI Themes & Radix UI Core (Cohérence avec Diktasis)
- **Styling**: Tailwind CSS (Cohérence avec Diktasis)
- **Analytics**: Google Analytics (Plan gratuit)
**Justification**:
- **Alignement avec les tendances et performances**: Next.js est un standard pour les sites modernes, performants et SEO-friendly, utilisé par des références comme Attio.com.
- **Optimisation des coûts**: Utilisation de solutions open source (Next.js, Strapi, Radix, Tailwind) et des plans gratuits/efficients de Vercel et Firebase. L'auto-hébergement de Strapi sur l'infrastructure GCP existante permet de mutualiser les coûts.
- **Expérience développeur et cohérence**: L'utilisation de React, Radix UI et Tailwind CSS assure une cohérence avec la stack technique de l'application Diktasis principale, facilitant la maintenance et le partage de connaissances/composants éventuels.
- **Contrôle et Flexibilité**: Strapi offre une grande flexibilité pour la gestion de contenu (blog, pages, etc.) et son auto-hébergement garantit le contrôle total des données.
- **Simplicité de déploiement**: Vercel simplifie grandement le déploiement et la maintenance du frontend Next.js.
- **Gestion des utilisateurs intégrée**: Firebase Authentication est une solution robuste et déjà connue dans l'écosystème du projet. 

## Formulaires Publics et Persistance des Données

### Configuration des Formulaires Publics
**Décision**: Utiliser `localStorage` pour stocker la configuration des formulaires générés par l'utilisateur dans le SmartForm Wizard, et une API mock pour les formulaires de démonstration.
**Justification**:
- **Simplicité pour le MVP**: Évite la mise en place immédiate d'une base de données complexe pour les configurations de formulaires personnalisés durant la phase de démonstration et de prototypage.
- **Expérience utilisateur fluide**: Permet de générer et de prévisualiser rapidement des liens de formulaires publics sans interaction serveur bloquante pour les formulaires créés par l'utilisateur.
- **API Mock pour la Stabilité des Démos**: `src/app/api/smart-forms/[formId]/route.ts` fournit des configurations de formulaires prédéfinies (`form-demo-1`, `form-demo-2`, `default-form`) qui ne dépendent pas du `localStorage` de l'utilisateur, assurant la robustesse des démonstrations.
- **Transition vers une Base de Données**: Cette approche permet de valider le concept. Une migration vers un stockage en base de données (via l'API `/api/smart-forms` existante qui a été étendue) est prévue pour une version de production afin d'assurer la persistance, le partage et la gestion centralisée des formulaires.

### Suivi du Statut de Complétion des Formulaires
**Décision**: Mettre à jour un champ `status: \'completed\'` dans la configuration du formulaire stockée dans `localStorage` (`formConfig-${formId}`) lorsque tous les champs requis d'un formulaire public sont soumis avec succès.
**Justification**:
- **Feedback Immédiat (Simulation)**: Permet de simuler un changement de statut visible sur le dashboard sans attendre une implémentation backend complète de la gestion des réponses et des statuts.
- **Cohérence avec la Persistance Locale**: S'appuie sur le mécanisme de `localStorage` déjà en place pour les configurations de formulaires.
- **Préparation pour le Backend**: La logique de vérification de la complétion des champs pourra être réutilisée côté serveur lorsque les soumissions de formulaires seront traitées par une API robuste.

### Envoi des Données vers les Sources (Simulation)
**Décision**: Sur la page de détail d'un SmartForm (`src/app/dashboard/smart-forms/[id]/page.tsx`), simuler l'action "Envoyer les données vers les sources" pour chaque réponse.
**Justification**:
- **Démonstration du Workflow**: Permet de montrer le concept de synchronisation des données collectées vers les applications tierces (CRM, etc.) sans implémenter les intégrations API réelles à ce stade.
- **Interface Utilisateur**: Met en place les éléments d'UI (bouton, notifications) qui seront utilisés lorsque la fonctionnalité sera complètement développée.
- **Focus sur le MVP**: Reporte la complexité des intégrations API réelles après la validation des fonctionnalités de base de collecte et de gestion des formulaires. 