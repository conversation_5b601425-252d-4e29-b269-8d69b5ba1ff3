'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Text<PERSON>ield, Flex, Badge, IconButton, Box, Text, Card, ScrollArea } from '@radix-ui/themes';
import { Cross2Icon, PersonIcon, CheckIcon, Pencil1Icon } from '@radix-ui/react-icons';

export interface Recipient {
  key: string;
  email: string;
  name?: string;
  type: 'crm' | 'manual';
  contactId?: number; // ID du contact CRM, si applicable
}

// Option pour les suggestions CRM
export interface CrmSuggestion extends Recipient {
  // Hérite de Recipient et peut être étendue si nécessaire
}

interface RecipientTagInputProps {
  recipients: Recipient[];
  onRecipientsChange: (recipients: Recipient[]) => void;
  placeholder?: string;
  label?: string;
  disabled?: boolean;
  required?: boolean;
  crmSuggestions?: CrmSuggestion[]; // Liste des contacts CRM pour l'auto-complétion
}

const isValidEmail = (email: string): boolean => {
  return /^[\w-\.+]+@([\w-]+\.)+[\w-]{2,4}$/.test(email);
};

const RecipientTagInput: React.FC<RecipientTagInputProps> = ({
  recipients,
  onRecipientsChange,
  placeholder = "Ajouter un email ou rechercher un contact...",
  label,
  disabled,
  required,
  crmSuggestions = [],
}) => {
  const [inputValue, setInputValue] = useState('');
  const [filteredSuggestions, setFilteredSuggestions] = useState<CrmSuggestion[]>([]);
  const [isSuggestionsOpen, setIsSuggestionsOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1); // Pour la navigation au clavier dans les suggestions

  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Nouveaux états pour l'édition
  const [editingRecipientKey, setEditingRecipientKey] = useState<string | null>(null);
  const [editInputValue, setEditInputValue] = useState('');
  const editInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputValue.length > 0 && crmSuggestions && crmSuggestions.length > 0) {
      const searchLower = inputValue.toLowerCase();
      
      const newFilteredSuggestions = crmSuggestions
        .filter(
          (s) => !recipients.some(r => r.email.toLowerCase() === s.email.toLowerCase())
        )
        .filter(
          (contact) =>
            contact.name?.toLowerCase().includes(searchLower) ||
            contact.email.toLowerCase().includes(searchLower)
        );

      setFilteredSuggestions(newFilteredSuggestions);
      setIsSuggestionsOpen(newFilteredSuggestions.length > 0);
    } else {
      setFilteredSuggestions([]);
      setIsSuggestionsOpen(false);
    }
  }, [inputValue, crmSuggestions, recipients]);

  // Gérer les clics en dehors pour fermer les suggestions
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        inputRef.current && !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
        editInputRef.current && !editInputRef.current.contains(event.target as Node) // Vérifier aussi le champ d'édition
      ) {
        if (editingRecipientKey) {
          handleSaveEdit(editingRecipientKey); // Sauvegarder si clic en dehors pendant l'édition
        } else {
          setIsSuggestionsOpen(false);
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [inputRef, suggestionsRef, editingRecipientKey, editInputRef, editInputValue]);

  // Mettre le focus sur le champ d'édition quand il apparaît
  useEffect(() => {
    if (editingRecipientKey && editInputRef.current) {
      editInputRef.current.focus();
      // Sélectionner le texte pour faciliter l'édition
      editInputRef.current.select(); 
    }
  }, [editingRecipientKey]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
    setActiveIndex(-1); // Réinitialiser l'index actif lors de la frappe
  };

  const addRecipientFromSuggestion = (suggestion: CrmSuggestion) => {
    setEditingRecipientKey(null); // Fermer l'édition si on ajoute une nouvelle pastille
    if (!recipients.find(r => r.email.toLowerCase() === suggestion.email.toLowerCase())) {
      const newRecipient: Recipient = {
        ...suggestion, // Conserver toutes les infos de la suggestion (y compris contactId)
        key: suggestion.key || Date.now().toString(), // Assurer une clé unique
        type: 'crm', // Marquer comme CRM
      };
      onRecipientsChange([...recipients, newRecipient]);
      setInputValue('');
      setIsSuggestionsOpen(false);
      setActiveIndex(-1);
      inputRef.current?.focus();
    } else {
      alert("Ce contact est déjà dans la liste.");
      setInputValue('');
      setIsSuggestionsOpen(false);
    }
  };

  const addManualRecipient = (emailValue: string) => {
    setEditingRecipientKey(null); // Fermer l'édition
    const email = emailValue.trim();
    if (email && isValidEmail(email) && !recipients.find(r => r.email.toLowerCase() === email.toLowerCase())) {
      const newRecipient: Recipient = {
        key: Date.now().toString(),
        email: email,
        name: email.split('@')[0],
        type: 'manual',
      };
      onRecipientsChange([...recipients, newRecipient]);
      setInputValue('');
      setIsSuggestionsOpen(false);
      setActiveIndex(-1);
    } else if (email && !isValidEmail(email)) {
      alert("L'adresse email saisie n'est pas valide.");
    } else if (email && recipients.find(r => r.email.toLowerCase() === email.toLowerCase())) {
      alert("Cette adresse email est déjà dans la liste.");
      setInputValue(''); // Vider le champ même si l'email est déjà présent
      setIsSuggestionsOpen(false);
    }
  };

  // Fonctions pour l'édition
  const startEdit = (recipient: Recipient) => {
    if (disabled || recipient.type !== 'manual') return;
    setEditingRecipientKey(recipient.key);
    setEditInputValue(recipient.email);
    setIsSuggestionsOpen(false); // Fermer les suggestions si ouvertes
    setInputValue(''); // Vider le champ d'input principal
  };

  const handleSaveEdit = (keyToUpdate: string) => {
    const trimmedEditValue = editInputValue.trim();
    if (!trimmedEditValue) { // Si vide, considérer comme une suppression
        removeRecipient(keyToUpdate);
        setEditingRecipientKey(null);
        setEditInputValue('');
        inputRef.current?.focus();
        return;
    }

    if (isValidEmail(trimmedEditValue)) {
      // Vérifier si le nouvel email n'est pas déjà pris par un AUTRE destinataire
      const otherRecipients = recipients.filter(r => r.key !== keyToUpdate);
      if (otherRecipients.some(r => r.email.toLowerCase() === trimmedEditValue.toLowerCase())) {
        alert("Cette adresse email est déjà utilisée par un autre destinataire.");
        // Optionnel: remettre le focus sur le champ d'édition
        // editInputRef.current?.focus();
        return;
      }

      const updatedRecipients = recipients.map(r =>
        r.key === keyToUpdate ? { ...r, email: trimmedEditValue, name: trimmedEditValue.split('@')[0] } : r
      );
      onRecipientsChange(updatedRecipients);
    } else {
      alert("L'adresse email modifiée n'est pas valide.");
      // Ne pas fermer le mode édition si l'email n'est pas valide pour permettre correction
      // Ou, si on veut être plus strict, on peut annuler l'édition ici.
      // Pour l'instant, on laisse l'utilisateur corriger.
      editInputRef.current?.focus();
      return; // Important pour ne pas reset les états d'édition
    }
    setEditingRecipientKey(null);
    setEditInputValue('');
    inputRef.current?.focus(); // Renvoyer le focus à l'input principal
  };

  const handleEditKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, keyToUpdate: string) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSaveEdit(keyToUpdate);
    }
    if (event.key === 'Escape') {
      event.preventDefault();
      setEditingRecipientKey(null);
      setEditInputValue('');
      inputRef.current?.focus();
    }
  };

  const removeRecipient = (keyToRemove: string) => {
    // Si on supprime la pastille en cours d'édition, sortir du mode édition
    if (editingRecipientKey === keyToRemove) {
      setEditingRecipientKey(null);
      setEditInputValue('');
    }
    onRecipientsChange(recipients.filter(recipient => recipient.key !== keyToRemove));
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;
    if (editingRecipientKey) return; // Ne pas traiter les keys de l'input principal si édition en cours

    if (isSuggestionsOpen && filteredSuggestions.length > 0) {
      if (event.key === 'ArrowDown') {
        event.preventDefault();
        setActiveIndex((prevIndex) => (prevIndex + 1) % filteredSuggestions.length);
        return;
      }
      if (event.key === 'ArrowUp') {
        event.preventDefault();
        setActiveIndex((prevIndex) => (prevIndex - 1 + filteredSuggestions.length) % filteredSuggestions.length);
        return;
      }
      if (event.key === 'Enter' && activeIndex >= 0) {
        event.preventDefault();
        addRecipientFromSuggestion(filteredSuggestions[activeIndex]);
        return;
      }
    }

    if (event.key === 'Enter' || event.key === ',') {
      event.preventDefault();
      if (inputValue.trim()) {
        addManualRecipient(inputValue);
      }
    }    
    if (event.key === 'Backspace' && inputValue === '' && recipients.length > 0) {
      event.preventDefault();
      // S'assurer de ne pas supprimer si on est en mode édition
      if (!editingRecipientKey) {
        removeRecipient(recipients[recipients.length - 1].key);
      }
    }
    if (event.key === 'Escape') {
      setIsSuggestionsOpen(false);
      setActiveIndex(-1);
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    if (editingRecipientKey) return; // Ne pas coller dans l'input principal si édition en cours
    event.preventDefault();
    const paste = event.clipboardData.getData('text');
    const emails = paste.split(/[,;\s]+/).filter(email => email.length > 0);
    let newRecipientsList = [...recipients];
    let addedCount = 0;
    emails.forEach(email => {
      const trimmedEmail = email.trim();
      if (trimmedEmail && isValidEmail(trimmedEmail) && !newRecipientsList.find(r => r.email.toLowerCase() === trimmedEmail.toLowerCase())) {
        newRecipientsList.push({
          key: Date.now().toString() + `_paste_${addedCount}`,
          email: trimmedEmail,
          name: trimmedEmail.split('@')[0],
          type: 'manual',
        });
        addedCount++;
      }
    });
    if (addedCount > 0) {
      onRecipientsChange(newRecipientsList);
    }
    setInputValue('');
    setIsSuggestionsOpen(false);
  };

  return (
    <Box style={{ position: 'relative' }}>
      {label && (
        <Text as="label" size="2" weight="medium" mb="1" style={{ display: 'block' }}>
          {label} {required && <Text color="red">*</Text>}
        </Text>
      )}
      <Box
        style={{
          border: '1px solid var(--gray-a7)',
          borderRadius: 'var(--radius-3)',
          padding: 'var(--space-2)',
          cursor: disabled ? 'not-allowed' : (editingRecipientKey ? 'default' : 'text'),
          backgroundColor: disabled ? 'var(--gray-a2)' : 'var(--color-background)',
          minHeight: '36px', // Pour éviter le saut de hauteur lors de l'édition
          display: 'flex', // Pour que le Flex interne prenne toute la hauteur
        }}
        onClick={() => {
          // Si on clique sur le Box général et qu'aucun champ d'édition n'est actif, focus sur l'input principal
          if (!disabled && !editingRecipientKey && inputRef.current) {
            inputRef.current.focus();
          }
          // Si on clique en dehors d'un champ d'édition (mais toujours dans le Box), on pourrait vouloir sauvegarder ?
          // Géré par handleClickOutside pour l'instant.
        }}
      >
        <Flex wrap="wrap" gap="2" align="center" style={{ width: '100%' }}>
          {recipients.map(recipient => (
            recipient.key === editingRecipientKey && recipient.type === 'manual' ? (
              <TextField.Root
                key={`edit-${recipient.key}`}
                ref={editInputRef}
                value={editInputValue}
                onChange={(e) => setEditInputValue(e.target.value)}
                onKeyDown={(e) => handleEditKeyDown(e, recipient.key)}
                onBlur={() => handleSaveEdit(recipient.key)} // Sauvegarder au onBlur
                disabled={disabled}
                size="1" // Taille plus petite pour s'intégrer
                style={{ 
                  flexGrow: 1, // Prendre la place disponible
                  minWidth: '150px',
                  boxShadow: 'none', // Enlever l'ombre du TextField pour mieux s'intégrer
                  border: '1px solid var(--accent-a7)' // Indiquer qu'il est en édition
                }}
                variant="surface"
                autoFocus
              />
            ) : (
              <Badge
                key={recipient.key}
                variant="soft"
                color={recipient.type === 'crm' ? "blue" : "gray"}
                radius="full"
                style={{
                  paddingRight: 'var(--space-1)',
                  userSelect: 'none',
                  cursor: recipient.type === 'manual' && !disabled ? 'pointer' : 'default',
                }}
                onClick={() => recipient.type === 'manual' && startEdit(recipient)}
              >
                {recipient.type === 'crm' && <PersonIcon width={12} height={12} style={{ marginRight: 'var(--space-1)' }} />}
                <Text size="2">{recipient.name || recipient.email}</Text>
                {!disabled && (
                  <IconButton
                    size="1"
                    variant="ghost"
                    color={recipient.type === 'crm' ? "blue" : "gray"}
                    radius="full"
                    onClick={(e) => { 
                      e.stopPropagation(); // Empêcher le déclenchement de startEdit
                      removeRecipient(recipient.key); 
                    }}
                    aria-label={`Supprimer ${recipient.email}`}
                    style={{ marginLeft: 'var(--space-1)', cursor: 'pointer' }}
                  >
                    <Cross2Icon width={12} height={12} />
                  </IconButton>
                )}
              </Badge>
            )
          ))}
          {!editingRecipientKey && (
            <TextField.Root
              ref={inputRef}
              id="recipient-input-field"
              style={{
                flexGrow: 1,
                minWidth: '200px',
                paddingLeft: recipients.length > 0 && !editingRecipientKey ? 'var(--space-1)' : '0',
                border: 'none',
                boxShadow: 'none',
              }}
              placeholder={recipients.length === 0 && !editingRecipientKey ? placeholder : ""}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              disabled={disabled}
              autoComplete="off"
              onFocus={() => {
                if (inputValue.length > 0 && filteredSuggestions.length > 0) {
                  setIsSuggestionsOpen(true);
                }
              }}
            />
          )}
        </Flex>
      </Box>
      {isSuggestionsOpen && filteredSuggestions.length > 0 && !disabled && !editingRecipientKey && (
        <Card
          ref={suggestionsRef}
          style={{
            position: 'absolute',
            width: '100%',
            maxHeight: '200px',
            overflowY: 'auto',
            zIndex: 100,
            marginTop: 'var(--space-1)',
            boxShadow: 'var(--shadow-4)',
          }}
        >
          <ScrollArea type="auto" scrollbars="vertical" style={{ maxHeight: '180px' }}>
            <Flex direction="column" gap="1">
              {filteredSuggestions.map((suggestion, index) => (
                <Box
                  key={suggestion.key || suggestion.email}
                  onClick={() => addRecipientFromSuggestion(suggestion)}
                  onMouseEnter={() => setActiveIndex(index)}
                  style={{
                    padding: 'var(--space-2) var(--space-3)',
                    cursor: 'pointer',
                    backgroundColor: index === activeIndex ? 'var(--accent-a3)' : 'transparent',
                    borderRadius: 'var(--radius-2)',
                  }}
                >
                  <Flex align="center" gap="2">
                    <PersonIcon width={14} height={14} color="var(--gray-11)" />
                    <Text size="2" weight="medium">{suggestion.name}</Text>
                    <Text size="2" color="gray">{suggestion.email}</Text>
                  </Flex>
                </Box>
              ))}
            </Flex>
          </ScrollArea>
        </Card>
      )}
    </Box>
  );
};

export default RecipientTagInput; 