# Configuration de Google Cloud Platform (GCP) pour Diktasis MVP

Ce document détaille les étapes de mise en place de l’environnement GCP pour le projet Diktasis MVP.

## Prérequis

- Un compte Google (ou une organisation) avec accès à la console GCP.
- L’outil de ligne de commande (gcloud) installé (voir [la doc officielle](https://cloud.google.com/sdk/docs/install)).

## Étapes de configuration

### 1. Création d’un projet GCP

- Ouvrez la [console GCP](https://console.cloud.google.com) et créez un nouveau projet (par exemple, nommé « diktasis-mvp »).
- Notez l’identifiant du projet (par exemple, « diktasis-mvp-xxxxx ») – il sera utilisé dans les commandes gcloud.

### 2. Activation des API nécessaires

Afin de pouvoir utiliser les services (Cloud Run, Cloud SQL, Firebase Auth, etc.), vous devez activer les API correspondantes. Par exemple, exécutez les commandes suivantes (en remplaçant par votre identifiant de projet) :

 (bash)
 gcloud config set project diktasis-mvp-xxxxx
 gcloud services enable run.googleapis.com
 gcloud services enable sqladmin.googleapis.com
 gcloud services enable firebase.googleapis.com
 (bash)

### 3. Mise en place de Cloud Run

- (Optionnel) Si vous souhaitez déployer votre application Next.js (backend) sur Cloud Run, vous pouvez suivre la [documentation officielle](https://cloud.google.com/run/docs/quickstarts/build-and-deploy) pour créer un conteneur (par exemple, via Dockerfile) et le déployer.
- Exemple de commande (à adapter selon votre Dockerfile) :
 (bash)
 gcloud builds submit --tag gcr.io/diktasis-mvp-xxxxx/diktasis-backend
 gcloud run deploy diktasis-backend --image gcr.io/diktasis-mvp-xxxxx/diktasis-backend --platform managed --region europe-west1 --allow-unauthenticated
 (bash)

### 4. Mise en place de Cloud SQL (PostgreSQL)

- Dans la console GCP, créez une instance Cloud SQL (PostgreSQL) (voir [la doc officielle](https://cloud.google.com/sql/docs/postgres/create-instance)).
- Notez l’adresse IP (ou le nom de connexion) de l’instance ainsi que les identifiants (utilisateur/mot de passe) – ils seront utilisés dans votre configuration (par exemple, via des variables d’environnement).

### 5. Intégration de Firebase Auth (pour l’authentification)

- Dans la console Firebase (associée à votre projet GCP), activez l’authentification (par exemple, via Google, email/mot de passe, etc.) (voir [la doc officielle](https://firebase.google.com/docs/auth)).
- Récupérez les clés (ou le fichier de configuration) Firebase (par exemple, via la console ou via la commande gcloud) afin de les intégrer dans votre application (par exemple, dans un fichier .env).

### 6. (Optionnel) Mise en place de Cloud Storage

Si vous avez besoin de stocker des fichiers (par exemple, des images, des documents, etc.), vous pouvez créer un bucket Cloud Storage (voir [la doc officielle](https://cloud.google.com/storage/docs/creating-buckets)) et configurer les permissions (IAM) en conséquence.

### 7. Sécurité et bonnes pratiques

- Utilisez des variables d’environnement (ou des secrets) pour stocker les clés API, les mots de passe, etc. (par exemple, via Cloud Secret Manager ou via un fichier .env non commité).
- Configurez les règles IAM (Identity and Access Management) afin de limiter l’accès aux ressources (par exemple, en utilisant des comptes de service avec des rôles minimaux).
- Consultez la [documentation officielle de GCP](https://cloud.google.com/docs) pour plus de détails sur la sécurité et les bonnes pratiques.

## Conclusion

Une fois ces étapes terminées, votre environnement GCP sera configuré pour le projet Diktasis MVP. Vous pourrez ensuite déployer votre application (backend, frontend, etc.) et intégrer les services (Cloud SQL, Firebase Auth, etc.) selon vos besoins. 