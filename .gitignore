# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Ignorer les répertoires générés (Next, node_modules, etc.)
.next
node_modules
# Ignorer les fichiers de configuration (par exemple, .env, .DS_Store, etc.)
.env
.DS_Store
# Ignorer les fichiers de log (npm, yarn, etc.)
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# Ignorer les fichiers de cache (par exemple, .cache, .fop, etc.)
.cache
.fop
# Ignorer les fichiers de configuration (par exemple, .cursor, .dbus-keyrings, etc.)
.cursor
.dbus-keyrings
# Ignorer les fichiers de propriétés (par exemple, .ranktracker.properties, .seopowersuite.properties, etc.)
.ranktracker.properties
.seopowersuite.properties
.websiteauditor.properties
# Ignorer les fichiers de données (par exemple, .ranktracker, .websiteauditor, etc.)
.ranktracker
.websiteauditor
# Ignorer les fichiers de configuration (par exemple, .redhat, .swt, etc.)
.redhat
.swt
# Ignorer les fichiers de données (par exemple, AppData, Contacts, etc.)
AppData
Contacts
Downloads
Dropbox
Favorites
Links
Mon Drive
Music
NTUSER.DAT*
NTUSER.ini
NTUSER.dat.LOG*
OneDrive
Saved Games
Searches
Tracing
Videos
# Ignorer les fichiers d'installation (par exemple, GoogleDriveSetup.exe, VuzeBittorrentClientInstaller.exe, etc.)
GoogleDriveSetup.exe
VuzeBittorrentClientInstaller.exe
teamwork-projects-desktop.exe
tws-latest-windows-x64.exe
vlc-3.0.21-win64.exe
# Ignorer les fichiers de log (par exemple, Sti_Trace.log, etc.)
Sti_Trace.log
# Ignorer les fichiers de données (par exemple, anomaly-monitor, frontend, hwid, etc.)
anomaly-monitor
frontend
hwid
