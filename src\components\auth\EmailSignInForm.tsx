"use client";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>Field, Flex, Text } from "@radix-ui/themes";
import { signInWithEmail, signUpWithEmail } from "../../firebaseConfig";
import { isAuthError, getErrorMessage, ValidationError } from "@/types/errors";
import { authLogger } from "@/utils/logger";

interface EmailSignInFormProps {
  onError: (error: string) => void;
  disabled?: boolean;
}

export const EmailSignInForm: React.FC<EmailSignInFormProps> = ({ 
  onError, 
  disabled = false 
}) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');

  const validateForm = () => {
    if (!email.trim()) {
      throw new ValidationError('Email requis', 'email');
    }
    if (!email.includes('@')) {
      throw new ValidationError('Email invalide', 'email');
    }
    if (!password.trim()) {
      throw new ValidationError('Mot de passe requis', 'password');
    }
    if (mode === 'signup' && password.length < 6) {
      throw new ValidationError('Le mot de passe doit contenir au moins 6 caractères', 'password');
    }
  };

  const handleEmailSignIn = async () => {
    setIsLoading(true);
    authLogger.info('Attempting email sign in', { email, mode });
    
    try {
      validateForm();
      
      if (mode === 'signin') {
        await signInWithEmail(email, password);
        authLogger.info('Email sign in successful', { email });
      } else {
        await signUpWithEmail(email, password);
        authLogger.info('Email sign up successful', { email });
      }
    } catch (error) {
      authLogger.error('Email authentication failed', { email, mode, error });
      if (isAuthError(error)) {
        onError(error.message);
      } else {
        onError(getErrorMessage(error));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = email.trim() && password.trim();

  return (
    <Flex direction="column" gap="3">
      <TextField.Root
        placeholder="Email"
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        disabled={disabled || isLoading}
        size="3"
      />
      
      <TextField.Root
        placeholder="Mot de passe"
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        disabled={disabled || isLoading}
        size="3"
      />
      
      <Button 
        onClick={handleEmailSignIn}
        disabled={disabled || isLoading || !isFormValid}
        loading={isLoading}
        size="3"
        style={{ width: '100%' }}
      >
        {mode === 'signin' ? 'Se connecter' : 'Créer un compte'}
      </Button>
      
      <Text 
        size="2" 
        align="center" 
        style={{ cursor: 'pointer', textDecoration: 'underline' }}
        onClick={() => setMode(mode === 'signin' ? 'signup' : 'signin')}
      >
        {mode === 'signin' 
          ? 'Pas de compte ? Créer un compte' 
          : 'Déjà un compte ? Se connecter'
        }
      </Text>
    </Flex>
  );
};
