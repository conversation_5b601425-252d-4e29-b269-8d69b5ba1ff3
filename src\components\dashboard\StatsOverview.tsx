import React from 'react';
import { DashboardStats, SmartForm, SmartFormResponse } from '../../types/dashboard';
import { mockDashboardStats, mockSmartForms } from '../../mock/data';
import { Box, Card, Flex, Grid, Text, Heading, Tooltip } from '@radix-ui/themes';
import {
  FileTextIcon,
  RocketIcon,
  CheckCircledIcon,
  BarChartIcon,
  StopwatchIcon,
  PaperPlaneIcon,
  ListBulletIcon,
  InfoCircledIcon
} from '@radix-ui/react-icons';

// Interface pour les props des icônes Radix (SVGProps<SVGSVGElement> est souvent utilisé)
interface RadixIconProps extends React.SVGProps<SVGSVGElement> {}

interface StatCardProps {
  name: string;
  value: string | number;
  icon: React.ReactElement<RadixIconProps>; // Icone est un élément React avec les props SVG
  colorName: "blue" | "green" | "yellow" | "purple" | "gray";
  description?: string;
  helpText?: string;
}

const StatCard: React.FC<StatCardProps> = ({ name, value, icon, colorName, description, helpText }) => {
  return (
    <Card size="2">
      <Flex gap="4" align="center">
        <Flex 
          align="center" 
          justify="center" 
          style={{
            width: 48, 
            height: 48, 
            borderRadius: 'var(--radius-3)', 
            backgroundColor: `var(--${colorName}-a3)`,
          }}
        >
          {/* Cloner l'icône pour lui ajouter/modifier des props */} 
          {React.cloneElement(icon, {
            width: 24,
            height: 24,
            color: `var(--${colorName}-11)`,
            // className: `text-${colorName}-11` // Alternative si la couleur directe ne fonctionne pas pour toutes les icônes
          })}
        </Flex>
        <Box style={{ flexGrow: 1 }}>
          <Flex align="center" gap="2" mb="1">
            <Text as="p" size="2" color="gray">{name}</Text>
            {helpText && (
              <Tooltip content={helpText}>
                <InfoCircledIcon width={14} height={14} color="var(--gray-9)" style={{ cursor: 'help' }} />
              </Tooltip>
            )}
          </Flex>
          <Heading as="h3" size="7" weight="bold" style={{ color: `var(--${colorName}-11)` }}>
            {value}
          </Heading>
          {description && (
            <Text size="1" color="gray" style={{ marginTop: 'var(--space-1)', display: 'block' }}>
              {description}
            </Text>
          )}
        </Box>
      </Flex>
    </Card>
  );
};

export const StatsOverview: React.FC = () => {
  // Calcul des KPIs opérationnels
  const smartFormsEnAttente = mockSmartForms.filter(form => {
    return form.status === 'sent' && 
           (!form.responses || form.responses.every(r => r.status === 'pending'));
  }).length;

  const completedResponses: { formSentAt: Date, responseSubmittedAt: Date }[] = [];
  mockSmartForms.forEach(form => {
    if (form.responses) {
      form.responses.forEach(response => {
        if (response.status === 'completed' && response.submittedAt) {
          completedResponses.push({ formSentAt: form.updatedAt, responseSubmittedAt: response.submittedAt });
        }
      });
    }
  });

  let delaiCompletionMoyenHeures = 0;
  if (completedResponses.length > 0) {
    const totalDelaiMillis = completedResponses.reduce((acc, curr) => {
      return acc + (curr.responseSubmittedAt.getTime() - curr.formSentAt.getTime());
    }, 0);
    delaiCompletionMoyenHeures = Math.round((totalDelaiMillis / completedResponses.length) / (1000 * 60 * 60));
  }
  // Si aucune réponse complétée, on peut afficher N/A ou 0, ou prendre la valeur mock
  // Pour l'instant, si pas de données, ça sera 0. On pourrait aussi prendre mockDashboardStats.averageResponseTime * 24

  const smartFormsAValider = mockSmartForms.reduce((acc, form) => {
    return acc + (form.responses ? form.responses.filter(r => r.status === 'completed').length : 0);
  }, 0);

  const statCardsData: StatCardProps[] = [
    {
      name: 'SmartForms en Attente',
      value: smartFormsEnAttente,
      icon: <PaperPlaneIcon />, 
      colorName: 'blue',
      description: 'Formulaires envoyés aux clients',
      helpText: 'Nombre de SmartForms envoyés à vos clients qui attendent encore une réponse. Vous pouvez relancer les clients si nécessaire.'
    },
    {
      name: 'Délai Complétion Moyen',
      value: completedResponses.length > 0 ? `${delaiCompletionMoyenHeures} h` : 'N/A', // Affiche N/A si pas de données, ajout de l'unité "h"
      icon: <StopwatchIcon />, 
      colorName: 'yellow',
      description: 'Temps de réponse client',
      helpText: 'Temps moyen entre l\'envoi d\'un SmartForm et sa complétion par le client. Un délai court indique une bonne expérience utilisateur.'
    },
    {
      name: 'SmartForms à Valider',
      value: smartFormsAValider,
      icon: <ListBulletIcon />, 
      colorName: 'purple',
      description: 'Réponses reçues à traiter',
      helpText: 'Nombre de réponses de SmartForms reçues qui nécessitent votre validation avant synchronisation avec vos outils.'
    },
  ];

  return (
    <Grid columns={{ initial: "1", sm: "2", md: "3" }} gap="4">
      {statCardsData.map((card, index) => (
        <StatCard key={index} {...card} />
      ))}
    </Grid>
  );
}; 