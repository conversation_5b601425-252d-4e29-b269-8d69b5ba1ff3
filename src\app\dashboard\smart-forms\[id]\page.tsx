'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  Button, 
  Flex, 
  Text, 
  Heading, 
  Card, 
  Badge, 
  Box,
  Separator,
  IconButton,
  Dialog,
  TextArea,
  Spinner,
  Tooltip
} from '@radix-ui/themes';
import { 
  CheckIcon,
  Cross2Icon,
  ChevronLeftIcon,
  EyeOpenIcon,
  DownloadIcon,
  ChatBubbleIcon,
  UpdateIcon
} from '@radix-ui/react-icons';
import { formatDateFR } from '@/utils/dateUtils';
import { SmartForm, SmartFormResponse, OdooField } from '@/types/dashboard';
import { FormConfig, FormField as PublicFormField } from '@/types/form';
import PublicFormRenderer from '@/components/form/PublicFormRenderer';
import { useSSE } from '@/hooks/useSSE';

export default function SmartFormDetailPage() {
  const router = useRouter();
  const params = useParams();
  const formId = params.id as string;

  const [form, setForm] = useState<SmartForm | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [selectedResponse, setSelectedResponse] = useState<SmartFormResponse | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [formPreviewConfig, setFormPreviewConfig] = useState<FormConfig | null>(null);
  const [isSendingToSources, setIsSendingToSources] = useState(false);
  const [sendToSourcesMessage, setSendToSourcesMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  const fetchData = useCallback(async () => {
    if (!formId) return;
    console.log(`[SmartFormDetailPage ${formId}] Fetching form details...`);
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/dashboard/smart-forms/${formId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Formulaire non trouvé.');
        } else {
          throw new Error(`Erreur HTTP ${response.status}: Impossible de charger les détails du formulaire.`);
        }
      }
      const data: SmartForm = await response.json();
      console.log(`[SmartFormDetailPage ${formId}] Form details received:`, data);
      setForm(data);
      if (data.fields && data.fields.length > 0) {
        const previewConfig: FormConfig = {
          formId: data.id,
          title: data.name,
          description: data.description,
          fields: data.fields.map(f => ({
            id: f.id,
            name: f.name,
            label: f.label || f.name,
            type: f.type as PublicFormField['type'],
            required: f.required,
            placeholder: f.placeholder,
            example: f.example,
            options: f.options?.map(opt => ({ value: String(opt.id), label: opt.name })) || [],
            readOnly: true,
            description: f.description,
          })),
          status: 'preview',
          submitUrl: '#',
          instructions: data.instructions,
          deadline: data.deadline
        };
        setFormPreviewConfig(previewConfig);
      }
    } catch (err: any) {
      console.error(`[SmartFormDetailPage ${formId}] Error fetching form details:`, err);
      setError(err.message);
      setForm(null);
    } finally {
      setIsLoading(false);
    }
  }, [formId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Activer les mises à jour en temps réel via SSE
  useSSE({
    onFormResponseAdded: useCallback((data: any) => {
      // Rafraîchir automatiquement si c'est notre formulaire
      if (data.formId === formId) {
        console.log(`[SSE] Nouvelle réponse pour le formulaire ${formId}, rafraîchissement automatique...`);
        fetchData();
      }
    }, [formId, fetchData])
  });

  const handleRefresh = () => {
    fetchData();
  };

  const handleCommentSubmit = async () => {
    console.log("Nouveau commentaire pour la réponse", selectedResponse?.id, ":", newComment);
    setIsSubmittingComment(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSubmittingComment(false);
    setNewComment('');
  };

  const handleSendDataToSources = async (responseId: string) => {
    if (!form || !form.responses) return;

    const responseData = form.responses.find(r => r.id === responseId);
    if (!responseData) {
      setSendToSourcesMessage({ type: 'error', text: "Données de réponse non trouvées." });
      return;
    }

    setIsSendingToSources(true);
    setSendToSourcesMessage(null);
    console.log(`[SmartFormDetailPage ${formId}] Tentative d'envoi des données de la réponse ${responseId} vers les sources...`, responseData.data);

    // TODO: Implémenter la logique d'appel API réelle ici
    // Pour la démo, on simule un appel
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simuler un succès ou un échec aléatoire pour la démo
    const isSuccess = Math.random() > 0.3; 

    if (isSuccess) {
      console.log(`[SmartFormDetailPage ${formId}] Envoi simulé des données de la réponse ${responseId} réussi.`);
      setSendToSourcesMessage({ type: 'success', text: "Les données ont été envoyées avec succès vers les sources configurées (simulation)." });
      // Optionnel: Mettre à jour le statut de la réponse ou du formulaire si nécessaire
      // Exemple: fetchData(); // Pour rafraîchir et voir un éventuel changement de statut
    } else {
      console.error(`[SmartFormDetailPage ${formId}] Echec simulé de l'envoi des données de la réponse ${responseId}.`);
      setSendToSourcesMessage({ type: 'error', text: "Échec de l'envoi des données vers les sources (simulation). Veuillez réessayer." });
    }

    setIsSendingToSources(false);
    setTimeout(() => setSendToSourcesMessage(null), 7000); // Cacher le message après 7s
  };

  if (isLoading) {
    return <Flex justify="center" align="center" style={{ minHeight: '50vh' }}><Spinner size="3" /></Flex>;
  }

  if (error) {
    return (
      <Flex direction="column" align="center" justify="center" gap="3" style={{ minHeight: '300px' }}>
        <Text color="tomato">Erreur: {error}</Text>
        <Button onClick={handleRefresh}><UpdateIcon /> Réessayer</Button>
      </Flex>
    );
  }

  if (!form) {
    return <Text>Formulaire non trouvé.</Text>;
  }
  
  const getStatusBadge = (status?: SmartForm['status']) => {
    if (!status) return <Badge color="gray">Inconnu</Badge>;
    switch (status) {
      case 'draft': return <Badge color="gray">Brouillon</Badge>;
      case 'sent': return <Badge color="blue">Envoyé</Badge>;
      case 'pending': return <Badge color="yellow">En attente</Badge>;
      case 'submitted': return <Badge color="green">Soumis</Badge>;
      case 'archived': return <Badge color="gray">Archivé</Badge>;
      case 'error': return <Badge color="red">Erreur</Badge>;
      case 'completed': return <Badge color="green">Complété</Badge>;
      default: 
        const _exhaustiveCheck: never = status;
        return <Badge color="gray">{status}</Badge>;
    }
  };

  return (
    <Box p={{ initial: '4', md: '6' }}>
      <Flex justify="between" align="center" mb="4">
        <Button variant="outline" onClick={() => router.back()}><ChevronLeftIcon /> Retour</Button>
        <Flex gap="3">
          <Tooltip content="Prévisualiser le formulaire public tel que le verra le destinataire">
            <Button variant="soft" onClick={() => setIsPreviewModalOpen(true)} disabled={!formPreviewConfig}>
              <EyeOpenIcon /> Prévisualiser
            </Button>
          </Tooltip>
          <Tooltip content="Rafraîchir les données">
            <IconButton variant="soft" onClick={handleRefresh}>
                <UpdateIcon />
            </IconButton>
          </Tooltip>
        </Flex>
      </Flex>
      
      <Heading mb="2">{form.name}</Heading>
      <Flex align="center" gap="3" mb="4">
        {getStatusBadge(form.status)}
        <Text size="2" color="gray">Créé le: {form.createdAt ? formatDateFR(form.createdAt) : 'N/A'}</Text>
        <Text size="2" color="gray">Dernière mise à jour: {form.updatedAt ? formatDateFR(form.updatedAt) : 'N/A'}</Text>
      </Flex>
      {form.description && <Text as="p" color="gray" mb="4">{form.description}</Text>}
      {form.instructions && (
        <Box mb="4" p="3" style={{ backgroundColor: 'var(--blue-a2)', borderRadius: 'var(--radius-3)' }}>
            <Text size="2" weight="medium" color="blue" mb="1">Instructions pour le destinataire :</Text>
            <Text size="2" color="gray" style={{whiteSpace: 'pre-wrap'}}>{form.instructions}</Text>
        </Box>
      )}
      {form.deadline && (
        <Text size="2" color="orange" mb="4">
            Date d'échéance souhaitée : {formatDateFR(form.deadline)}
        </Text>
      )}

      <Heading size="4" mb="3" mt="6">Réponses ({form.responses?.length || 0})</Heading>
      {form.responses && form.responses.length > 0 ? (
        form.responses.map(response => (
          <Card key={response.id} mb="3">
            <Box p="4">
              <Flex justify="between" align="start" mb="2">
                  <Box>
                      <Text size="2" color="gray">ID Réponse: {response.id}</Text><br/>
                      <Text size="2" color="gray">Soumise le: {response.submittedAt ? formatDateFR(response.submittedAt) : 'N/A'}</Text>
                  </Box>
                  <Button 
                    size="1" 
                    variant="outline" 
                    onClick={() => handleSendDataToSources(response.id)}
                    disabled={isSendingToSources || response.status === 'sent_to_sources'}
                    highContrast={response.status !== 'sent_to_sources'}
                  >
                    {isSendingToSources && <Spinner size="1" />} 
                    {response.status === 'sent_to_sources' ? <><CheckIcon width="12"/> Données Envoyées</> : "Envoyer vers Sources"}
                  </Button>
              </Flex>
              {Object.entries(response.data).map(([key, value]) => {
                  const fieldDefinition = form.fields.find(f => f.id === key);
                  return (
                      <Box key={key} mb="2" py="1" style={{borderBottom: '1px solid var(--gray-a3)'}}>
                          <Text size="2" weight="medium">{fieldDefinition?.label || fieldDefinition?.name || key}:</Text><br/>
                          <Text size="2" color="gray" style={{whiteSpace: 'pre-wrap'}}>{String(value)}</Text>
                      </Box>
                  );
              })}
            </Box>
          </Card>
        ))
      ) : (
        <Text color="gray">Aucune réponse pour ce formulaire pour le moment.</Text>
      )}

      {/* Notification pour l'envoi vers les sources */} 
      {sendToSourcesMessage && (
        <Box 
          style={{
            position: 'fixed',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            padding: 'var(--space-3) var(--space-4)',
            borderRadius: 'var(--radius-3)',
            backgroundColor: sendToSourcesMessage.type === 'success' ? 'var(--green-9)' : 'var(--red-9)',
            color: 'white',
            boxShadow: 'var(--shadow-5)',
            zIndex: 1000,
            textAlign: 'center'
          }}
        >
          <Text size="2" weight="medium">{sendToSourcesMessage.text}</Text>
        </Box>
      )}

      <Dialog.Root open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
        <Dialog.Content style={{ maxWidth: '800px', width: '90vw', maxHeight: '85vh', display: 'flex', flexDirection: 'column' }}>
          <Dialog.Title mb="3">Aperçu du Formulaire Public</Dialog.Title>
          <Box style={{ flexGrow: 1, overflowY: 'auto', paddingRight: 'var(--space-2)', marginRight: '-var(--space-2)'}}>
            {formPreviewConfig ? (
                <PublicFormRenderer 
                    formConfig={formPreviewConfig} 
                    formData={{}}
                    onInputChange={() => {}}
                    onSubmit={async (e) => e.preventDefault()}
                    isSubmitting={false}
                    submissionError={null}
                />
            ) : (
                <Text>Configuration de l'aperçu non disponible.</Text>
            )}
          </Box>
          <Dialog.Close>
            <Button mt="4" variant="soft" style={{marginLeft: 'auto'}}>Fermer</Button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Root>
    </Box>
  );
} 