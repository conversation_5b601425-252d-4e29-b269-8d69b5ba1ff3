import React from 'react';
import { <PERSON>, Button, Flex, Select, TextField, Badge, Text } from '@radix-ui/themes';
import { MagnifyingGlassIcon, Cross2Icon, CalendarIcon } from '@radix-ui/react-icons';

interface SmartFormsFiltersProps {
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  search: string;
  setSearch: (search: string) => void;
  dateFilter: string;
  setDateFilter: (date: string) => void;
  totalResults: number;
  onClearFilters: () => void;
}

const statusOptions = [
  { value: 'all', label: 'Tous les statuts', count: 0 },
  { value: 'draft', label: 'Brouillons', count: 0 },
  { value: 'sent', label: 'Envoyés', count: 0 },
  { value: 'completed', label: 'Complétés', count: 0 }
];

const dateOptions = [
  { value: 'all', label: 'Toutes les dates' },
  { value: 'today', label: 'Aujourd\'hui' },
  { value: 'week', label: '<PERSON><PERSON> semaine' },
  { value: 'month', label: 'Ce mois' },
  { value: 'quarter', label: 'Ce trimestre' }
];

export const SmartFormsFilters: React.FC<SmartFormsFiltersProps> = ({
  statusFilter,
  setStatusFilter,
  search,
  setSearch,
  dateFilter,
  setDateFilter,
  totalResults,
  onClearFilters
}) => {
  const hasActiveFilters = statusFilter !== 'all' || search !== '' || dateFilter !== 'all';

  return (
    <Box mb="6">
      <Flex direction="column" gap="4">
        {/* Ligne principale des filtres */}
        <Flex 
          direction={{ initial: 'column', md: 'row' }} 
          gap="3" 
          align={{ md: 'center' }}
          wrap="wrap"
        >
          {/* Recherche */}
          <TextField.Root 
            placeholder="Rechercher par nom, description..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            size="2"
            style={{ minWidth: 280 }}
          >
            <TextField.Slot>
              <MagnifyingGlassIcon height="16" width="16" />
            </TextField.Slot>
          </TextField.Root>

          {/* Filtre par statut */}
          <Select.Root value={statusFilter} onValueChange={setStatusFilter} size="2">
            <Select.Trigger placeholder="Filtrer par statut..." style={{ minWidth: 160 }} />
            <Select.Content>
              {statusOptions.map(option => (
                <Select.Item key={option.value} value={option.value}>
                  {option.label}
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>

          {/* Filtre par date */}
          <Select.Root value={dateFilter} onValueChange={setDateFilter} size="2">
            <Select.Trigger placeholder="Filtrer par date..." style={{ minWidth: 160 }}>
              <CalendarIcon height="16" width="16" style={{ marginRight: '8px' }} />
            </Select.Trigger>
            <Select.Content>
              {dateOptions.map(option => (
                <Select.Item key={option.value} value={option.value}>
                  {option.label}
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>

          {/* Bouton pour effacer les filtres */}
          {hasActiveFilters && (
            <Button 
              variant="ghost" 
              color="gray" 
              size="2"
              onClick={onClearFilters}
            >
              <Cross2Icon height="16" width="16" />
              Effacer les filtres
            </Button>
          )}
        </Flex>

        {/* Ligne des résultats et filtres actifs */}
        <Flex justify="between" align="center" wrap="wrap" gap="3">
          <Text size="2" color="gray">
            {totalResults} formulaire{totalResults > 1 ? 's' : ''} trouvé{totalResults > 1 ? 's' : ''}
          </Text>
          
          {/* Badges des filtres actifs */}
          <Flex gap="2" wrap="wrap">
            {statusFilter !== 'all' && (
              <Badge variant="soft" color="blue" size="1">
                Statut: {statusOptions.find(s => s.value === statusFilter)?.label}
                <Button 
                  variant="ghost" 
                  size="1" 
                  style={{ marginLeft: '4px', padding: '2px' }}
                  onClick={() => setStatusFilter('all')}
                >
                  <Cross2Icon height="12" width="12" />
                </Button>
              </Badge>
            )}
            {dateFilter !== 'all' && (
              <Badge variant="soft" color="purple" size="1">
                Date: {dateOptions.find(d => d.value === dateFilter)?.label}
                <Button 
                  variant="ghost" 
                  size="1" 
                  style={{ marginLeft: '4px', padding: '2px' }}
                  onClick={() => setDateFilter('all')}
                >
                  <Cross2Icon height="12" width="12" />
                </Button>
              </Badge>
            )}
            {search && (
              <Badge variant="soft" color="green" size="1">
                Recherche: "{search}"
                <Button 
                  variant="ghost" 
                  size="1" 
                  style={{ marginLeft: '4px', padding: '2px' }}
                  onClick={() => setSearch('')}
                >
                  <Cross2Icon height="12" width="12" />
                </Button>
              </Badge>
            )}
          </Flex>
        </Flex>
      </Flex>
    </Box>
  );
}; 