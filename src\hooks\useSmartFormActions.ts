import { useRouter } from 'next/navigation';
import { useDeleteSmartForm } from './useSmartForms';
import { logger } from '@/utils/logger';

export const useSmartFormActions = () => {
  const router = useRouter();
  const deleteSmartFormMutation = useDeleteSmartForm();

  const handleView = (formId: string) => {
    logger.info('Navigating to smart form details', 'SmartFormActions', { formId });
    router.push(`/dashboard/smart-forms/${formId}`);
  };

  const handleDuplicate = async (formId: string) => {
    logger.info('Duplicating smart form', 'SmartFormActions', { formId });
    // TODO: Implémenter la duplication
    // 1. Récupérer le formulaire original
    // 2. Créer une copie avec un nouveau nom
    // 3. Rediriger vers le nouveau formulaire
    alert('Fonctionnalité de duplication à implémenter');
  };

  const handleShare = async (formId: string) => {
    logger.info('Sharing smart form', 'SmartFormActions', { formId });
    // TODO: Implémenter le partage
    // 1. Générer un lien de partage
    // 2. Copier dans le presse-papier
    // 3. Afficher une notification
    const shareUrl = `${window.location.origin}/${formId}`;
    
    try {
      await navigator.clipboard.writeText(shareUrl);
      alert(`Lien copié dans le presse-papier: ${shareUrl}`);
    } catch (error) {
      logger.error('Failed to copy share link', 'SmartFormActions', error);
      alert(`Lien de partage: ${shareUrl}`);
    }
  };

  const handleDelete = async (formId: string, formName: string) => {
    logger.info('Attempting to delete smart form', 'SmartFormActions', { formId, formName });
    
    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir supprimer le formulaire "${formName}" ?\n\nCette action est irréversible.`
    );
    
    if (!confirmed) {
      logger.info('Smart form deletion cancelled by user', 'SmartFormActions', { formId });
      return;
    }

    try {
      await deleteSmartFormMutation.mutateAsync(formId);
      logger.info('Smart form deleted successfully', 'SmartFormActions', { formId, formName });
      // La mutation se charge automatiquement de mettre à jour le cache
    } catch (error) {
      logger.error('Failed to delete smart form', 'SmartFormActions', { formId, error });
      alert('Erreur lors de la suppression du formulaire');
    }
  };

  return {
    handleView,
    handleDuplicate,
    handleShare,
    handleDelete,
    isDeleting: deleteSmartFormMutation.isPending,
  };
};
