import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { smartFormsKeys } from './useSmartForms';
import { apiLogger } from '@/utils/logger';

interface SSEEvent {
  type: string;
  data: any;
}

interface SSEOptions {
  onFormResponseAdded?: (data: any) => void;
}

/**
 * Hook pour écouter les événements Server-Sent Events
 * et invalider automatiquement le cache React Query
 */
export const useSSE = (options?: SSEOptions) => {
  const queryClient = useQueryClient();
  const eventSourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    // Créer la connexion SSE
    const eventSource = new EventSource('/api/events');
    eventSourceRef.current = eventSource;

    // Gestionnaire pour la connexion établie
    eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      apiLogger.info('SSE connexion établie', data);
    });

    // Gestionnaire pour les nouvelles réponses de formulaire
    eventSource.addEventListener('form-response-added', (event) => {
      const data = JSON.parse(event.data);
      apiLogger.info('Nouvelle réponse de formulaire reçue via SSE', data);

      // Invalider le cache pour la liste des SmartForms
      queryClient.invalidateQueries({ queryKey: smartFormsKeys.lists() });

      // Invalider le cache pour le formulaire spécifique
      if (data.formId) {
        queryClient.invalidateQueries({ queryKey: smartFormsKeys.detail(data.formId) });
      }

      // Appeler le callback personnalisé si fourni
      if (options?.onFormResponseAdded) {
        options.onFormResponseAdded(data);
      }

      console.log(`[SSE] Cache invalidé pour le formulaire ${data.formId} - ${data.responseCount} réponses`);
    });

    // Gestionnaire pour les heartbeats (optionnel)
    eventSource.addEventListener('heartbeat', (event) => {
      // Juste pour maintenir la connexion, pas besoin de log
    });

    // Gestionnaire d'erreurs
    eventSource.onerror = (error) => {
      apiLogger.error('Erreur SSE', error);
      
      // Reconnecter automatiquement après 5 secondes
      setTimeout(() => {
        if (eventSource.readyState === EventSource.CLOSED) {
          apiLogger.info('Tentative de reconnexion SSE');
          // La reconnexion se fera automatiquement lors du prochain useEffect
        }
      }, 5000);
    };

    // Nettoyage lors du démontage du composant
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
        apiLogger.info('Connexion SSE fermée');
      }
    };
  }, [queryClient, options]);

  // Fonction pour fermer manuellement la connexion
  const closeConnection = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
  };

  return { closeConnection };
};

/**
 * Hook simplifié pour les composants qui veulent juste activer SSE
 */
export const useRealtimeUpdates = () => {
  return useSSE();
};
