"use client";
import { Callout } from "@radix-ui/themes";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";

interface ErrorDisplayProps {
  error: string | null;
  onDismiss?: () => void;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onDismiss }) => {
  if (!error) return null;

  return (
    <Callout.Root color="red" style={{ marginBottom: '16px' }}>
      <Callout.Icon>
        <ExclamationTriangleIcon />
      </Callout.Icon>
      <Callout.Text>
        {error}
        {onDismiss && (
          <span 
            style={{ 
              marginLeft: '8px', 
              cursor: 'pointer', 
              textDecoration: 'underline' 
            }}
            onClick={onDismiss}
          >
            Fermer
          </span>
        )}
      </Callout.Text>
    </Callout.Root>
  );
};
