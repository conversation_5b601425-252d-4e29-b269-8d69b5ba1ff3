# Cas d'Usage et Profils Utilisateurs

## Qu'est-ce qu'un SmartForm ?

Un SmartForm est une demande d'information intelligente qui :
- Se connecte aux sources de données existantes (CRM, ERP, etc.)
- Permet une sélection personnalisée des champs requis
- Utilise l'IA pour suggérer des champs pertinents
- Offre une expérience utilisateur fluide et sécurisée
- Synchronise automatiquement les données validées
- Peut être réutilisé comme modèle

## Profils Utilisateurs

### 1. Agences
Les agences sont les utilisateurs principaux qui gèrent plusieurs clients et projets.

**Caractéristiques :**
- Gestion de plusieurs clients
- Besoin d'une vue d'ensemble
- Intégration avec leurs outils existants
- Suivi des demandes en cours

**Permissions :**
- Création et gestion des demandes
- Accès aux données clients
- Configuration des intégrations CRM
- Gestion des accès utilisateurs

### 2. Clients Finaux
Les clients finaux sont les destinataires des demandes d'informations.

**Caractéristiques :**
- Accès ponctuel à la plateforme
- Besoin de simplicité
- Partage d'informations sensibles
- Suivi de l'état des demandes

**Permissions :**
- Réponse aux demandes
- Visualisation de l'historique
- Gestion de leurs informations
- Téléchargement des documents

### 3. Administrateurs
Les administrateurs gèrent la plateforme et ses utilisateurs.

**Caractéristiques :**
- Gestion globale de la plateforme
- Configuration des paramètres
- Monitoring des activités
- Support utilisateurs

**Permissions :**
- Gestion des utilisateurs
- Configuration système
- Accès aux logs
- Gestion des intégrations

## Hiérarchie des Utilisateurs et Permissions

### 1. Super Administrateur (Niveau 1)
**Rôle :** Gestion globale de la plateforme et des intégrations

**Caractéristiques :**
- Accès complet à toutes les fonctionnalités
- Gestion des intégrations système
- Configuration des connecteurs API
- Définition des mappings de champs
- Gestion des permissions globales

**Permissions Spécifiques :**
- Création et gestion des types d'utilisateurs
- Configuration des règles de sécurité
- Accès aux logs système
- Gestion des clés API
- Configuration des webhooks
- Définition des templates de champs par intégration
- Gestion des environnements (dev, staging, prod)

### 2. Administrateur d'Agence (Niveau 2)
**Rôle :** Gestion d'une agence et de ses utilisateurs

**Caractéristiques :**
- Gestion des utilisateurs de l'agence
- Configuration des intégrations CRM/ERP
- Définition des modèles de SmartForms
- Gestion des accès et permissions

**Permissions Spécifiques :**
- Création de comptes utilisateurs
- Configuration des intégrations CRM
- Définition des modèles de SmartForms
- Gestion des permissions internes
- Accès aux statistiques de l'agence
- Configuration des notifications

### 3. Utilisateur Standard (Niveau 3)
**Rôle :** Utilisation quotidienne de la plateforme

**Caractéristiques :**
- Création et gestion de SmartForms
- Interaction avec les clients
- Suivi des demandes

**Permissions Spécifiques :**
- Création de SmartForms
- Envoi de demandes
- Gestion des réponses
- Accès aux données clients
- Utilisation des modèles existants

### 4. Client Final (Niveau 4)
**Rôle :** Destinataire des SmartForms

**Caractéristiques :**
- Accès limité aux formulaires
- Réponse aux demandes
- Visualisation de l'historique

**Permissions Spécifiques :**
- Réponse aux SmartForms
- Visualisation de l'historique
- Gestion des informations personnelles

## Gestion des Intégrations

### Configuration des Connecteurs
1. **Super Administrateur**
   - Création des connecteurs API
   - Configuration des endpoints
   - Définition des mappings de champs
   - Gestion des authentifications
   - Configuration des webhooks

2. **Administrateur d'Agence**
   - Activation des connecteurs
   - Configuration des credentials
   - Personnalisation des mappings
   - Gestion des synchronisations

### Gestion des Champs
1. **Niveau Super Admin**
   - Définition des champs disponibles par intégration
   - Configuration des validations
   - Gestion des transformations de données
   - Définition des règles de synchronisation

2. **Niveau Admin Agence**
   - Sélection des champs pour les SmartForms
   - Personnalisation des validations
   - Configuration des règles métier

## Matrice des Permissions

| Fonctionnalité | Super Admin | Admin Agence | Utilisateur | Client |
|----------------|-------------|--------------|-------------|---------|
| Gestion des intégrations | ✅ | ⚠️ | ❌ | ❌ |
| Configuration des champs | ✅ | ⚠️ | ❌ | ❌ |
| Création de SmartForms | ✅ | ✅ | ✅ | ❌ |
| Gestion des utilisateurs | ✅ | ⚠️ | ❌ | ❌ |
| Accès aux logs | ✅ | ⚠️ | ❌ | ❌ |
| Configuration système | ✅ | ❌ | ❌ | ❌ |
| Gestion des modèles | ✅ | ✅ | ⚠️ | ❌ |
| Réponse aux formulaires | ✅ | ✅ | ✅ | ✅ |

⚠️ = Permissions limitées à leur scope

## Cas d'Usage Principaux

### 1. Onboarding d'une Agence

**Acteurs :** Administrateur, Agence

**Scénario :**
1. L'administrateur crée un compte pour l'agence
2. L'agence reçoit un email d'invitation
3. L'agence complète son profil :
   - Informations de l'entreprise
   - Configuration des intégrations CRM
   - Définition des modèles de demandes
4. L'agence invite ses collaborateurs
5. L'agence crée sa première demande

**Points d'attention :**
- Validation des informations de l'agence
- Configuration sécurisée des accès CRM
- Personnalisation des modèles
- Formation initiale

### 2. Création d'une Demande

**Acteurs :** Agence

**Scénario :**
1. L'agence sélectionne un modèle de demande
2. Configuration de la demande :
   - Sélection des champs requis
   - Définition des destinataires
   - Paramètres de confidentialité
3. Envoi de la demande
4. Suivi des réponses

**Points d'attention :**
- Validation des champs obligatoires
- Gestion des destinataires multiples
- Paramètres de confidentialité
- Notifications automatiques

### 3. Réponse à une Demande

**Acteurs :** Client Final

**Scénario :**
1. Le client reçoit une notification
2. Accès à la plateforme via un lien sécurisé
3. Visualisation de la demande
4. Remplissage des informations :
   - Saisie des données
   - Upload de documents
   - Validation des informations
5. Soumission de la réponse

**Points d'attention :**
- Expérience utilisateur simplifiée
- Validation en temps réel
- Sécurité des données
- Confirmation de soumission

### 4. Synchronisation CRM

**Acteurs :** Système, Agence

**Scénario :**
1. Réception d'une réponse client
2. Validation des données
3. Enrichissement automatique :
   - Vérification des données
   - Complétion des informations manquantes
4. Synchronisation avec le CRM :
   - Mise à jour des contacts
   - Création des opportunités
   - Mise à jour des documents

**Points d'attention :**
- Gestion des erreurs de synchronisation
- Conflits de données
- Historique des modifications
- Notifications de succès/échec

### 5. Remplissage d'un Formulaire Public par un Destinataire Externe

**Acteurs :** Destinataire Externe (personne ne possédant pas de compte sur la plateforme Diktasis), Agence (initiateur du formulaire)

**Scénario :**
1.  L'Agence, via le SmartForm Wizard, configure un formulaire (titre, description, champs à collecter, instructions, etc.).
2.  L'Agence génère un lien public unique pour ce formulaire (ex: `/form-demo-1` pour les démos, ou `/custom-XYZ` pour les formulaires créés par l'utilisateur).
3.  L'Agence communique ce lien au Destinataire Externe (par email, messagerie, etc.).
4.  Le Destinataire Externe ouvre le lien dans son navigateur.
5.  La page du formulaire (`src/app/[formId]/page.tsx`) charge la configuration :
    *   Pour les formulaires `/custom-XYZ`, elle tente de lire la configuration depuis `localStorage` (clé `formConfig-custom-XYZ`).
    *   Si non trouvée localement ou pour les formulaires de démo, elle appelle l'API mock (`/api/smart-forms/[formId]`) pour récupérer la configuration.
6.  Le formulaire est affiché au Destinataire Externe, avec les champs définis, les instructions et la date d'échéance si applicable.
7.  Le Destinataire Externe remplit les champs :
    *   Validation en temps réel des champs requis.
    *   Les données saisies sont stockées temporairement dans l'état du composant React.
8.  Le Destinataire Externe soumet le formulaire.
9.  Les données sont envoyées à l'API mock (`POST /api/smart-forms/[formId]`) qui simule la réception et renvoie un succès.
10. Si tous les champs requis ont été remplis, le statut du formulaire (dans `localStorage` pour les formulaires `custom-XYZ`) est mis à jour en "complété".
11. Un message de succès s'affiche pour le Destinataire Externe.
12. L'Agence peut ensuite consulter le statut "complété" sur son dashboard et, dans une future itération, voir les données soumises et les envoyer vers les sources de données connectées.

**Points d'attention :**
- Simplicité et clarté de l'interface pour le Destinataire Externe.
- Sécurité et confidentialité des données (même si la soumission est simulée pour le MVP).
- Gestion des erreurs de chargement du formulaire ou de soumission.
- Robustesse de la récupération de configuration (localStorage + fallback API).
- Expérience utilisateur sur mobile.

## Cas d'Usage Détaillés

### Cas d'Usage : Agent Immobilier et SmartForm

**Acteurs :** 
- Justine (Agent Immobilier)
- Client Final
- Système Diktasis
- Odoo (ERP)

**Contexte :**
Justine est agent immobilier utilisant Odoo comme ERP. Elle cherche à optimiser la collecte et la validation des données clients.

**Scénario Détaillé :**

#### 1. Onboarding et Configuration
1. Inscription via Google/Email
2. Assistant IA pour la connexion Odoo
3. Accès au Dashboard avec option SmartForm

#### 2. Création du SmartForm
1. Sélection des champs Odoo disponibles
2. Suggestions IA basées sur le contexte
3. Personnalisation des champs :
   - Dénomination sociale
   - Adresse complète
   - Email de facturation
   - Numéro de TVA
   - Téléphone
4. Sauvegarde du modèle

#### 3. Distribution
1. Génération de lien sécurisé
2. Options d'envoi :
   - Copie du lien (WhatsApp, etc.)
   - Email direct via Diktasis
3. Notification client

#### 4. Collecte des Données
1. Accès client au formulaire
2. Champs pré-remplis si disponibles
3. Modification/complétion des données
4. Validation par le client

#### 5. Traitement et Synchronisation
1. Notification à Justine
2. Revue des modifications
3. Validation des données
4. Synchronisation automatique avec Odoo

#### 6. Réutilisation
1. Sauvegarde comme modèle
2. Adaptation pour différents contextes :
   - Devis
   - Contrats
   - Factures
   - Ouverture de dossier

**Bénéfices :**
- ⏱ Gain de temps significatif
- ✅ Données fiables et validées
- 🤖 Expérience utilisateur fluide
- 🧩 Process modulaire et réutilisable

**Points d'Attention :**
- Sécurité des données sensibles
- Validation des modifications
- Gestion des erreurs de synchronisation
- Formation des utilisateurs

## Métriques de Succès

### Pour les Agences
- Temps de configuration initial
- Nombre de demandes actives
- Taux de réponse des clients
- Qualité des données collectées
- Temps de création d'un SmartForm
- Taux de réutilisation des modèles
- Qualité des suggestions IA

### Pour les Clients Finaux
- Temps de réponse
- Facilité d'utilisation
- Taux de complétion
- Satisfaction utilisateur
- Temps de complétion du SmartForm
- Taux de satisfaction des suggestions IA

### Pour les Administrateurs
- Taux d'adoption
- Nombre d'incidents
- Temps de résolution
- Satisfaction des utilisateurs

## Prochaines Étapes

1. **Validation des Cas d'Usage**
   - Revue avec les parties prenantes
   - Ajustement des scénarios
   - Priorisation des fonctionnalités
   - Validation de la matrice des permissions

2. **Développement des Parcours**
   - Création des wireframes
   - Prototypage des interfaces
   - Tests utilisateurs
   - Implémentation du système de permissions

3. **Documentation Technique**
   - Spécifications détaillées
   - Diagrammes de séquence
   - API endpoints
   - Documentation des intégrations

4. **Plan de Déploiement**
   - Phases de développement
   - Tests et validation
   - Formation des utilisateurs
   - Migration des données 