'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  Button, 
  Flex, 
  Text, 
  Heading, 
  Card, 
  Badge, 
  Select, 
  TextField, 
  Box,
  TextArea,
  Separator,
  Progress
} from '@radix-ui/themes';
import { 
  CheckIcon,
  ExclamationTriangleIcon,
  InfoCircledIcon,
  PaperPlaneIcon
} from '@radix-ui/react-icons';
import { formatDateFR } from '@/utils/dateUtils';

// Types pour les champs
interface Field {
  id: string;
  name: string;
  description: string;
  group: string;
  required: boolean;
  type: 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea';
  options?: string[];
}

interface FieldGroup {
  id: string;
  name: string;
  description: string;
  icon: string;
  fields: Field[];
}

interface FormData {
  [fieldId: string]: string;
}

interface SmartFormData {
  id: string;
  title: string;
  description: string;
  instructions?: string;
  deadline?: string;
  agencyName: string;
  clientName: string;
  selectedFields: string[];
  requiredFields: string[]; // Champs marqués comme obligatoires par le créateur
  status: 'active' | 'expired' | 'completed';
}

// Données mockées pour les champs (même structure que dans le wizard)
const mockFieldGroups: FieldGroup[] = [
  {
    id: 'general',
    name: 'Informations Générales',
    description: 'Informations de base sur l\'entreprise',
    icon: '🏢',
    fields: [
      { id: 'company_name', name: 'Nom de l\'entreprise', description: 'Raison sociale officielle', group: 'general', required: true, type: 'text' },
      { id: 'siret', name: 'Numéro SIRET', description: 'Numéro SIRET de l\'entreprise', group: 'general', required: true, type: 'text' },
      { id: 'legal_form', name: 'Forme juridique', description: 'SARL, SAS, EURL, etc.', group: 'general', required: true, type: 'select', options: ['SARL', 'SAS', 'EURL', 'SA', 'SNC', 'Autre'] },
      { id: 'creation_date', name: 'Date de création', description: 'Date de création de l\'entreprise', group: 'general', required: false, type: 'date' },
      { id: 'activity_code', name: 'Code APE/NAF', description: 'Code d\'activité principale', group: 'general', required: true, type: 'text' },
    ]
  },
  {
    id: 'contact',
    name: 'Informations de Contact',
    description: 'Coordonnées et contacts principaux',
    icon: '📞',
    fields: [
      { id: 'main_address', name: 'Adresse du siège', description: 'Adresse complète du siège social', group: 'contact', required: true, type: 'textarea' },
      { id: 'phone', name: 'Téléphone principal', description: 'Numéro de téléphone principal', group: 'contact', required: true, type: 'text' },
      { id: 'email', name: 'Email principal', description: 'Adresse email principale', group: 'contact', required: true, type: 'email' },
      { id: 'website', name: 'Site web', description: 'URL du site web de l\'entreprise', group: 'contact', required: false, type: 'text' },
    ]
  },
  {
    id: 'accounting',
    name: 'Comptabilité',
    description: 'Informations comptables essentielles',
    icon: '📊',
    fields: [
      { id: 'fiscal_year_end', name: 'Clôture exercice', description: 'Date de clôture de l\'exercice fiscal', group: 'accounting', required: true, type: 'date' },
      { id: 'accounting_software', name: 'Logiciel comptable', description: 'Logiciel utilisé pour la comptabilité', group: 'accounting', required: true, type: 'select', options: ['Sage', 'Ciel', 'EBP', 'QuickBooks', 'Odoo', 'Autre'] },
      { id: 'chart_of_accounts', name: 'Plan comptable', description: 'Plan comptable utilisé', group: 'accounting', required: true, type: 'select', options: ['PCG 2014', 'Plan comptable simplifié', 'Plan spécifique', 'Autre'] },
      { id: 'accountant_name', name: 'Expert-comptable', description: 'Nom du cabinet comptable', group: 'accounting', required: false, type: 'text' },
    ]
  },
  {
    id: 'tax',
    name: 'Fiscalité',
    description: 'Informations fiscales et TVA',
    icon: '💰',
    fields: [
      { id: 'vat_number', name: 'Numéro de TVA', description: 'Numéro de TVA intracommunautaire', group: 'tax', required: true, type: 'text' },
      { id: 'vat_system', name: 'Régime de TVA', description: 'Régime de TVA applicable', group: 'tax', required: true, type: 'select', options: ['Réel normal', 'Réel simplifié', 'Franchise en base', 'Autre'] },
      { id: 'tax_center', name: 'Centre des impôts', description: 'Centre des impôts de rattachement', group: 'tax', required: true, type: 'text' },
      { id: 'tax_representative', name: 'Représentant fiscal', description: 'Nom du représentant fiscal si applicable', group: 'tax', required: false, type: 'text' },
    ]
  },
  {
    id: 'banking',
    name: 'Informations Bancaires',
    description: 'Coordonnées bancaires principales',
    icon: '🏦',
    fields: [
      { id: 'bank_name', name: 'Banque principale', description: 'Nom de la banque principale', group: 'banking', required: true, type: 'text' },
      { id: 'iban', name: 'IBAN', description: 'Numéro IBAN du compte principal', group: 'banking', required: true, type: 'text' },
      { id: 'bic', name: 'BIC/SWIFT', description: 'Code BIC/SWIFT de la banque', group: 'banking', required: true, type: 'text' },
      { id: 'account_holder', name: 'Titulaire du compte', description: 'Nom du titulaire du compte', group: 'banking', required: true, type: 'text' },
    ]
  }
];

// Fonction pour simuler la récupération des données du SmartForm
const getSmartFormData = (id: string): SmartFormData => {
  return {
    id,
    title: 'Collecte des informations comptables 2024',
    description: 'Nous avons besoin de mettre à jour vos informations comptables pour la nouvelle année. Merci de remplir ce formulaire avec les informations les plus récentes.',
    instructions: 'Veuillez remplir tous les champs obligatoires. Si vous avez des questions, n\'hésitez pas à nous contacter.',
    deadline: '2024-12-31',
    agencyName: 'Cabinet Expertise Comptable',
    clientName: 'Innovatech Ltd.',
    selectedFields: [
      'company_name', 'siret', 'legal_form', 'activity_code',
      'main_address', 'phone', 'email',
      'fiscal_year_end', 'accounting_software', 'chart_of_accounts',
      'vat_number', 'vat_system', 'tax_center',
      'bank_name', 'iban', 'bic'
    ],
    // Simulation des champs obligatoires définis par le créateur
    requiredFields: [
      'company_name', 'siret', 'legal_form', 'activity_code',
      'main_address', 'phone', 'email',
      'fiscal_year_end', 'accounting_software',
      'vat_number', 'vat_system',
      'bank_name', 'iban'
    ],
    status: 'active'
  };
};

export default function SmartFormClientPage() {
  const params = useParams();
  const router = useRouter();
  const formId = params.id as string;
  
  const [smartForm, setSmartForm] = useState<SmartFormData | null>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    // Simuler le chargement des données du SmartForm
    const data = getSmartFormData(formId);
    setSmartForm(data);
    
    // Pré-remplir avec des données existantes (simulation)
    setFormData({
      company_name: 'Innovatech Ltd.',
      email: '<EMAIL>',
      phone: '01 23 45 67 89'
    });
  }, [formId]);

  if (!smartForm) {
    return (
      <Box style={{ maxWidth: 'var(--max-width-2xl)', margin: '0 auto' }} py="8">
        <Card>
          <Box p="6">
            <Text>Chargement du formulaire...</Text>
          </Box>
        </Card>
      </Box>
    );
  }

  if (smartForm.status === 'expired') {
    return (
      <Box style={{ maxWidth: 'var(--max-width-2xl)', margin: '0 auto' }} py="8">
        <Card>
          <Box p="6">
            <Flex align="center" gap="3" mb="4">
              <ExclamationTriangleIcon width={24} height={24} color="orange" />
              <Heading size="6" color="orange">Formulaire expiré</Heading>
            </Flex>
            <Text size="3" color="gray">
              Ce formulaire a expiré et ne peut plus être rempli. 
              Veuillez contacter {smartForm.agencyName} pour obtenir un nouveau lien.
            </Text>
          </Box>
        </Card>
      </Box>
    );
  }

  if (isSubmitted) {
    return (
      <Box style={{ maxWidth: 'var(--max-width-2xl)', margin: '0 auto' }} py="8">
        <Card>
          <Box p="6">
            <Flex align="center" gap="3" mb="4">
              <CheckIcon width={24} height={24} color="green" />
              <Heading size="6" color="green">Formulaire envoyé avec succès !</Heading>
            </Flex>
            <Text size="3" color="gray" mb="4">
              Merci d'avoir rempli ce formulaire. Vos informations ont été transmises à {smartForm.agencyName}.
            </Text>
            <Text size="2" color="gray">
              Vous recevrez une confirmation par email sous peu.
            </Text>
          </Box>
        </Card>
      </Box>
    );
  }

  // Obtenir tous les champs sélectionnés avec leurs détails
  const allFields = mockFieldGroups.flatMap(group => group.fields);
  const selectedFields = smartForm.selectedFields
    .map(fieldId => allFields.find(field => field.id === fieldId))
    .filter(Boolean) as Field[];

  // Grouper les champs par groupe
  const fieldsByGroup = selectedFields.reduce((acc, field) => {
    if (!acc[field.group]) {
      acc[field.group] = [];
    }
    acc[field.group].push(field);
    return acc;
  }, {} as Record<string, Field[]>);

  // Calculer la progression basée sur les champs obligatoires définis par le créateur
  const requiredFieldIds = smartForm.requiredFields;
  const requiredFields = selectedFields.filter(field => requiredFieldIds.includes(field.id));
  const completedRequiredFields = requiredFields.filter(field => 
    formData[field.id] && formData[field.id].trim() !== ''
  );
  const progress = requiredFields.length > 0 
    ? (completedRequiredFields.length / requiredFields.length) * 100 
    : 100;

  const handleFieldChange = (fieldId: string, value: string) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    
    // Supprimer l'erreur si le champ est maintenant rempli
    if (errors[fieldId] && value.trim() !== '') {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Valider uniquement les champs marqués comme obligatoires par le créateur
    requiredFields.forEach(field => {
      const value = formData[field.id];
      if (!value || value.trim() === '') {
        newErrors[field.id] = 'Ce champ est obligatoire';
      } else if (field.type === 'email' && !value.includes('@')) {
        newErrors[field.id] = 'Veuillez entrer une adresse email valide';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    // Simuler l'envoi
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('Données du formulaire soumises:', {
      formId: smartForm.id,
      data: formData
    });
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const renderField = (field: Field) => {
    const value = formData[field.id] || '';
    const hasError = !!errors[field.id];
    
    const commonProps = {
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
        handleFieldChange(field.id, e.target.value),
      size: "3" as const,
      style: hasError ? { borderColor: 'var(--red-8)' } : undefined
    };

    switch (field.type) {
      case 'textarea':
        return (
          <TextArea
            {...commonProps}
            placeholder={field.description}
            rows={3}
          />
        );
      
      case 'select':
        return (
          <Select.Root value={value} onValueChange={(val) => handleFieldChange(field.id, val)}>
            <Select.Trigger placeholder={field.description} />
            <Select.Content>
              {field.options?.map(option => (
                <Select.Item key={option} value={option}>
                  {option}
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>
        );
      
      default:
        return (
          <TextField.Root
            {...commonProps}
            type={field.type}
            placeholder={field.description}
          />
        );
    }
  };

  return (
    <Box style={{ maxWidth: 'var(--max-width-2xl)', margin: '0 auto' }} py="8">
      {/* En-tête */}
      <Card mb="6">
        <Box p="6">
          <Flex align="center" gap="3" mb="4">
            <Text size="8">📋</Text>
            <Box>
              <Heading size="7" mb="1">{smartForm.title}</Heading>
              <Text size="3" color="gray">
                Demandé par {smartForm.agencyName} • {smartForm.clientName}
              </Text>
            </Box>
          </Flex>
          
          {smartForm.description && (
            <Text size="3" color="gray" mb="4">
              {smartForm.description}
            </Text>
          )}
          
          {smartForm.instructions && (
            <Card variant="surface" size="1" mb="4">
              <Flex align="start" gap="3" p="3">
                <InfoCircledIcon width={16} height={16} color="blue" style={{ marginTop: '2px' }} />
                <Box>
                  <Text size="2" weight="medium" mb="1">Instructions :</Text>
                  <Text size="2" color="gray">{smartForm.instructions}</Text>
                </Box>
              </Flex>
            </Card>
          )}
          
          {smartForm.deadline && (
            <Text size="2" color="gray">
                             Date limite : {formatDateFR(smartForm.deadline)}
            </Text>
          )}
        </Box>
      </Card>

      {/* Indicateur de progression */}
      <Card mb="6">
        <Box p="4">
          <Flex justify="between" align="center" mb="3">
            <Text size="3" weight="medium">Progression</Text>
            <Text size="2" color="gray">
              {completedRequiredFields.length} / {requiredFields.length} champs obligatoires
            </Text>
          </Flex>
          <Progress value={progress} size="2" />
        </Box>
      </Card>

      {/* Formulaire */}
      <form onSubmit={handleSubmit}>
        <Flex direction="column" gap="6">
          {Object.entries(fieldsByGroup).map(([groupId, fields]) => {
            const group = mockFieldGroups.find(g => g.id === groupId);
            if (!group) return null;

            return (
              <Card key={groupId}>
                <Box p="6">
                  <Flex align="center" gap="3" mb="4">
                    <Text size="6">{group.icon}</Text>
                    <Box>
                      <Heading size="5" weight="medium">{group.name}</Heading>
                      <Text size="2" color="gray">{group.description}</Text>
                    </Box>
                  </Flex>
                  
                  <Flex direction="column" gap="4">
                    {fields.map(field => (
                      <Box key={field.id}>
                        <Flex align="center" gap="2" mb="2">
                          <Text as="label" size="3" weight="medium">
                            {field.name}
                          </Text>
                          {smartForm.requiredFields.includes(field.id) && (
                            <Badge color="red" variant="soft" size="1">Obligatoire</Badge>
                          )}
                        </Flex>
                        
                        {renderField(field)}
                        
                        {errors[field.id] && (
                          <Text size="2" color="red" mt="1">
                            {errors[field.id]}
                          </Text>
                        )}
                      </Box>
                    ))}
                  </Flex>
                </Box>
              </Card>
            );
          })}
        </Flex>

        {/* Bouton de soumission */}
        <Card mt="6">
          <Box p="6">
            <Flex justify="center">
              <Button
                type="submit"
                size="4"
                variant="solid"
                color="blue"
                disabled={isSubmitting || progress < 100}
                loading={isSubmitting}
              >
                <PaperPlaneIcon width={20} height={20} />
                {isSubmitting ? 'Envoi en cours...' : 'Envoyer le formulaire'}
              </Button>
            </Flex>
            
            {progress < 100 && (
              <Text size="2" color="gray" align="center" mt="3" style={{ display: 'block' }}>
                Veuillez remplir tous les champs obligatoires pour pouvoir envoyer le formulaire.
              </Text>
            )}
          </Box>
        </Card>
      </form>
    </Box>
  );
} 