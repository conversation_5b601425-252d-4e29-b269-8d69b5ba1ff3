import { NextResponse } from 'next/server';
import { mockSmartForms } from '@/mock/data';
import { SmartForm, SmartFormResponse } from '@/types/dashboard';

// Simulation: En production, vous récupéreriez cela d'une base de données
const findFormById = (id: string): SmartForm | undefined => {
  console.log(`API /api/dashboard/smart-forms/${id}: Recherche du formulaire avec ID: ${id}`);
  const form = mockSmartForms.find(form => form.id === id);
  console.log(`API /api/dashboard/smart-forms/${id}: Formulaire trouvé:`, JSON.stringify(form, null, 2));
  return form;
};

// Cette fonction n'est plus nécessaire si les réponses sont déjà dans mockSmartForms
// const findResponsesByFormId = (formId: string): SmartFormResponse[] => {
//   // Assurez-vous que mockSmartFormResponses est défini dans votre fichier mock/data.ts
//   // et qu'il contient des objets SmartFormResponse.
//   // return mockSmartFormResponses.filter((response: SmartFormResponse) => response.formId === formId);
//   return []; // Placeholder, sera supprimé
// };

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const formId = params.id;
  console.log(`API /api/dashboard/smart-forms/${formId}: Requête GET reçue pour l'ID: ${formId}`);

  // TODO: Ajouter la logique d'authentification pour s'assurer que l'utilisateur a accès à ce formulaire

  try {
    const form = findFormById(formId);

    if (!form) {
      console.warn(`API /api/dashboard/smart-forms/${formId}: Formulaire non trouvé.`);
      return new NextResponse('Formulaire non trouvé', { status: 404 });
    }

    // Les réponses sont déjà incluses dans l'objet form grace à la structure de mockSmartForms
    // Si form.responses est undefined, on renvoie un tableau vide pour être cohérent avec le type SmartForm
    const formWithResponses: SmartForm = {
      ...form,
      responses: form.responses || [], 
    };
    console.log(`API /api/dashboard/smart-forms/${formId}: Données renvoyées au client:`, JSON.stringify(formWithResponses, null, 2));
    return NextResponse.json(formWithResponses);
  } catch (error) {
    console.error(`API /api/dashboard/smart-forms/${formId}: Erreur lors de la récupération des détails du formulaire:`, error);
    return new NextResponse('Erreur interne du serveur', { status: 500 });
  }
} 