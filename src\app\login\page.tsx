"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
// Remplacer par vos vraies fonctions d'auth plus tard
import { signInWithGoogle, signInWithEmail, signUpWithEmail, observeUser } from "@/firebaseConfig";

export default function LoginPage() {
  const [mode, setMode] = useState<'login' | 'signup'>("login");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  // Rediriger automatiquement si l'utilisateur est déjà connecté
  useEffect(() => {
    if (!authLoading && user) {
      router.push('/dashboard');
    }
  }, [user, authLoading, router]);

  // Afficher un écran de chargement pendant la vérification de l'authentification
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-200 via-pink-200 to-purple-200">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Vérification de votre connexion...</p>
        </div>
      </div>
    );
  }

  const handleGoogle = async () => {
    setError(null);
    setLoading(true);
    try {
      await signInWithGoogle();
      // La redirection sera gérée automatiquement par useEffect
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    try {
      if (mode === "login") {
        await signInWithEmail(email, password);
      } else {
        await signUpWithEmail(email, password);
      }
      // La redirection sera gérée automatiquement par useEffect
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-200 via-pink-200 to-purple-200">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-xl p-8">
        <h1 className="text-2xl font-bold text-center mb-6">
          {mode === "login" ? "Connectez-vous à votre compte" : "Créez votre compte"}
        </h1>
        <form className="space-y-4" onSubmit={handleEmail}>
          <div>
            <label className="block text-sm font-medium text-gray-700">E-mail</label>
            <input
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Mot de passe</label>
            <input
              type="password"
              autoComplete={mode === "login" ? "current-password" : "new-password"}
              required
              value={password}
              onChange={e => setPassword(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base"
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input id="remember_me" name="remember_me" type="checkbox" className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
              <label htmlFor="remember_me" className="ml-2 block text-sm text-gray-900">Se souvenir de moi</label>
            </div>
            {mode === "login" && (
              <a href="#" className="text-sm text-indigo-600 hover:text-indigo-500">Mot de passe oublié ?</a>
            )}
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
          >
            {loading ? "Chargement..." : mode === "login" ? "Connexion" : "Créer un compte"}
          </button>
        </form>
        <div className="flex items-center my-4">
          <div className="flex-grow border-t border-gray-200"></div>
          <span className="mx-2 text-gray-400 text-sm">OU</span>
          <div className="flex-grow border-t border-gray-200"></div>
        </div>
        <button
          onClick={handleGoogle}
          disabled={loading}
          className="w-full flex items-center justify-center gap-2 py-2 px-4 border border-gray-300 rounded-md bg-white text-gray-700 font-medium shadow-sm hover:bg-gray-50 cursor-pointer"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_993_122)">
              <path d="M19.8052 10.2309C19.8052 9.5508 19.7491 8.86727 19.629 8.19824H10.2V12.0491H15.6191C15.3932 13.2727 14.6522 14.3364 13.6013 15.0455V17.0455H16.6013C18.4013 15.3818 19.8052 13.0455 19.8052 10.2309Z" fill="#4285F4"/>
              <path d="M10.2 20C12.7 20 14.8013 19.1818 16.6013 17.0455L13.6013 15.0455C12.6013 15.6818 11.4013 16.0455 10.2 16.0455C7.8013 16.0455 5.8013 14.3636 5.1013 12.1818H2.0013V14.2545C3.8013 17.3636 6.8013 20 10.2 20Z" fill="#34A853"/>
              <path d="M5.1013 12.1818C4.8013 11.4545 4.6013 10.6818 4.6013 9.90909C4.6013 9.13636 4.8013 8.36364 5.1013 7.63636V5.56364H2.0013C1.36364 6.78182 1 8.31818 1 9.90909C1 11.5 1.36364 13.0364 2.0013 14.2545L5.1013 12.1818Z" fill="#FBBC05"/>
              <path d="M10.2 3.77273C11.5013 3.77273 12.7013 4.22727 13.6013 5.04545L16.6013 2.04545C14.8013 0.318182 12.7 0 10.2 0C6.8013 0 3.8013 2.63636 2.0013 5.56364L5.1013 7.63636C5.8013 5.45455 7.8013 3.77273 10.2 3.77273Z" fill="#EA4335"/>
            </g>
            <defs>
              <clipPath id="clip0_993_122">
                <rect width="20" height="20" fill="white"/>
              </clipPath>
            </defs>
          </svg>
          {mode === "login" ? "Me connecter avec Google" : "Créer un compte avec Google"}
        </button>
        {error && <p className="text-red-500 text-sm mt-4 text-center">{error}</p>}
        <div className="mt-6 text-center text-sm text-gray-500">
          {mode === "login" ? (
            <>
              Vous n'avez pas de compte ?{' '}
              <button className="text-indigo-600 hover:underline cursor-pointer" onClick={() => setMode('signup')}>Créer un compte</button>
            </>
          ) : (
            <>
              Vous avez déjà un compte ?{' '}
              <button className="text-indigo-600 hover:underline cursor-pointer" onClick={() => setMode('login')}>Se connecter</button>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 