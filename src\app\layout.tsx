import type { Metada<PERSON> } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "@radix-ui/themes/styles.css";
import { Theme } from '@radix-ui/themes';
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { QueryProvider } from "@/providers/QueryProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Diktasis",
  description: "Plateforme de gestion documentaire sécurisée",
  icons: {
    icon: "/favicon.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr">
      <head>
        <link rel="icon" href="/favicon.svg" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <QueryProvider>
          <AuthProvider>
            <Theme appearance="light" accentColor="blue" grayColor="slate" scaling="100%">
              {children}
            </Theme>
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
