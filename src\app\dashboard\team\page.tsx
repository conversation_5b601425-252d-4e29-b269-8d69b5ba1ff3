import React from 'react';
const mockTeam = [
  { id: 1, name: '<PERSON><PERSON>', role: '<PERSON><PERSON>', email: '<EMAIL>' },
  { id: 2, name: '<PERSON>', role: '<PERSON>laborateur', email: '<EMAIL>' },
  { id: 3, name: '<PERSON>', role: '<PERSON>laborateur', email: '<EMAIL>' },
];
export default function TeamPage() {
  return (
    <div className="max-w-2xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Équipe</h1>
      <ul className="space-y-4">
        {mockTeam.map(member => (
          <li key={member.id} className="bg-white rounded-xl shadow p-4 flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">{member.name}</div>
              <div className="text-xs text-gray-500">{member.role} — {member.email}</div>
            </div>
            <button className="px-3 py-1 rounded-md text-sm font-semibold bg-red-100 text-red-700 hover:bg-red-200 cursor-pointer">Retirer</button>
          </li>
        ))}
      </ul>
      <button className="mt-6 w-full bg-indigo-600 text-white rounded-md py-2 font-semibold hover:bg-indigo-700 transition cursor-pointer">Ajouter un membre</button>
    </div>
  );
} 