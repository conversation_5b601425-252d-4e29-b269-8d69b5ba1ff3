import React, { useMemo } from 'react';
import {
  Badge,
  Box,
  Button,
  Card,
  Flex,
  Table,
  Text,
  Tooltip,
  DropdownMenu,
  IconButton
} from '@radix-ui/themes';
import {
  ClockIcon,
  CheckCircledIcon,
  ExclamationTriangleIcon,
  DotsHorizontalIcon,
  EyeOpenIcon,
  CopyIcon,
  Share1Icon,
  TrashIcon,
  InfoCircledIcon
} from '@radix-ui/react-icons';
import { SmartForm } from '../../types/dashboard';
import { formatDateFR } from '../../utils/dateUtils';
import { useRouter } from 'next/navigation';
import { logger } from '../../utils/logger';
import { useSmartFormActions } from '../../hooks/useSmartFormActions';

interface SmartFormsTableProps {
  smartForms: SmartForm[];
}

export const SmartFormsTable: React.FC<SmartFormsTableProps> = React.memo(({ smartForms }) => {
  const router = useRouter();
  const { handleView, handleDuplicate, handleShare, handleDelete, isDeleting } = useSmartFormActions();

  // Memoization des configurations de statut pour éviter les re-créations
  const statusConfig = useMemo(() => ({
    draft: {
      color: 'gray' as const,
      icon: <ClockIcon width={12} height={12} />,
      label: 'Brouillon'
    },
    sent: {
      color: 'blue' as const,
      icon: <Share1Icon width={12} height={12} />,
      label: 'Envoyé'
    },
    pending: {
      color: 'yellow' as const,
      icon: <ExclamationTriangleIcon width={12} height={12} />,
      label: 'En attente'
    },
    submitted: {
      color: 'green' as const,
      icon: <CheckCircledIcon width={12} height={12} />,
      label: 'Soumis'
    },
    completed: {
      color: 'green' as const,
      icon: <CheckCircledIcon width={12} height={12} />,
      label: 'Complété'
    },
    archived: {
      color: 'gray' as const,
      icon: <TrashIcon width={12} height={12} />,
      label: 'Archivé'
    },
    error: {
      color: 'red' as const,
      icon: <ExclamationTriangleIcon width={12} height={12} />,
      label: 'Erreur'
    }
  }), []);

  const getStatusBadge = useMemo(() => (status?: SmartForm['status']) => {
    if (!status || !Object.prototype.hasOwnProperty.call(statusConfig, status)) {
      logger.warn('Statut SmartForm inconnu ou non géré', 'SmartFormsTable', { status, type: typeof status });
      return (
        <Badge color="gray" variant="soft" size="1">
          <InfoCircledIcon width={12} height={12} />
          Inconnu
        </Badge>
      );
    }

    const config = statusConfig[status];
    return (
      <Badge color={config.color} variant="soft" size="1">
        {config.icon}
        {config.label}
      </Badge>
    );
  }, [statusConfig]);

  const getProgressWidth = (form: SmartForm) => {
    if (form.status === 'draft') return '20%';
    if (form.status === 'sent') return '60%';
    if (form.status === 'submitted') return '100%';
    if (form.status === 'completed') return '100%';
    if (form.status === 'pending') return '40%';
    return '0%';
  };

  const getProgressColor = (form: SmartForm) => {
    if (form.status === 'draft') return 'var(--gray-9)';
    if (form.status === 'sent') return 'var(--blue-9)';
    if (form.status === 'submitted') return 'var(--green-9)';
    if (form.status === 'completed') return 'var(--green-9)';
    if (form.status === 'pending') return 'var(--yellow-9)';
    return 'var(--gray-6)';
  };

  const getLastActivity = (form: SmartForm) => {
    if (form.responses && form.responses.length > 0) {
      const lastResponse = form.responses[form.responses.length - 1];
      if (lastResponse.submittedAt) {
        return formatDateFR(lastResponse.submittedAt);
      }
    }
    return formatDateFR(form.updatedAt);
  };

  const getResponsesCount = (form: SmartForm) => {
    if (!form.responses) return 0;
    return form.responses.filter(r => r.status === 'completed').length;
  };

  const handleAction = (action: string, form: SmartForm) => {
    switch (action) {
      case 'view':
        handleView(form.id);
        break;
      case 'duplicate':
        handleDuplicate(form.id);
        break;
      case 'share':
        handleShare(form.id);
        break;
      case 'delete':
        handleDelete(form.id, form.name);
        break;
    }
  };

  if (smartForms.length === 0) {
    return (
      <Card size="3">
        <Flex direction="column" align="center" justify="center" gap="3" style={{ minHeight: 200 }}>
          <Text size="4" color="gray">Aucun SmartForm trouvé</Text>
          <Text size="2" color="gray" align="center">
            Ajustez vos filtres ou créez votre premier SmartForm
          </Text>
          <Button 
            size="2" 
            onClick={() => router.push('/dashboard/new-smart-forms')}
          >
            Créer un SmartForm
          </Button>
        </Flex>
      </Card>
    );
  }

  return (
    <Card size="3">
      <Table.Root variant="surface" size="2">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell>
              <Flex align="center" gap="2">
                Formulaire
                <Tooltip content="Nom et description du SmartForm">
                  <InfoCircledIcon width={14} height={14} color="var(--gray-9)" />
                </Tooltip>
              </Flex>
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Statut</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>
              <Flex align="center" gap="2">
                Progression
                <Tooltip content="Avancement du processus de collecte">
                  <InfoCircledIcon width={14} height={14} color="var(--gray-9)" />
                </Tooltip>
              </Flex>
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Réponses</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Créé le</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Dernière activité</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">Actions</Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {smartForms.map((form) => (
              <Table.Row key={form.id}>
                <Table.Cell>
                  <Box>
                    <Text weight="medium" size="2" as="div" style={{ marginBottom: '2px' }}>
                      {form.name}
                    </Text>
                    {form.description && (
                      <Text size="1" color="gray" as="div" style={{ opacity: 0.8 }}>
                        {form.description}
                      </Text>
                    )}
                    {form.template && (
                      <Badge color="purple" variant="outline" size="1" style={{ marginTop: '4px' }}>
                        Modèle
                      </Badge>
                    )}
                  </Box>
                </Table.Cell>
                <Table.Cell>
                  {getStatusBadge(form.status)}
                </Table.Cell>
                <Table.Cell>
                  <Box style={{ width: 80 }}>
                    <Box 
                      style={{ 
                        width: '100%', 
                        height: 6, 
                        backgroundColor: 'var(--gray-a3)', 
                        borderRadius: 'var(--radius-2)',
                        overflow: 'hidden'
                      }}
                    >
                      <Box 
                        style={{ 
                          height: '100%', 
                          width: getProgressWidth(form),
                          backgroundColor: getProgressColor(form),
                          transition: 'width 0.3s ease'
                        }}
                      />
                    </Box>
                  </Box>
                </Table.Cell>
                <Table.Cell>
                  <Text size="2" color="gray">
                    {getResponsesCount(form)}
                    {form.responses && form.responses.length > 0 && (
                      <Text size="1" color="gray" style={{ opacity: 0.7 }}>
                        /{form.responses.length}
                      </Text>
                    )}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <Text size="2" color="gray">
                    {formatDateFR(form.createdAt)}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <Text size="2" color="gray">
                    {getLastActivity(form)}
                  </Text>
                </Table.Cell>
                <Table.Cell align="right">
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <IconButton
                        variant="ghost"
                        color="gray"
                        size="2"
                        disabled={isDeleting}
                      >
                        <DotsHorizontalIcon width={16} height={16} />
                      </IconButton>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content>
                      <DropdownMenu.Item onClick={() => handleAction('view', form)}>
                        <EyeOpenIcon width={14} height={14} />
                        Voir les détails
                      </DropdownMenu.Item>
                      <DropdownMenu.Item onClick={() => handleAction('duplicate', form)}>
                        <CopyIcon width={14} height={14} />
                        Dupliquer
                      </DropdownMenu.Item>
                      <DropdownMenu.Item onClick={() => handleAction('share', form)}>
                        <Share1Icon width={14} height={14} />
                        Partager
                      </DropdownMenu.Item>
                      <DropdownMenu.Separator />
                      <DropdownMenu.Item
                        color="red"
                        onClick={() => handleAction('delete', form)}
                        disabled={isDeleting}
                      >
                        <TrashIcon width={14} height={14} />
                        {isDeleting ? 'Suppression...' : 'Supprimer'}
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                </Table.Cell>
              </Table.Row>
            ))}
        </Table.Body>
      </Table.Root>
    </Card>
  );
});

SmartFormsTable.displayName = 'SmartFormsTable';