import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, Flex, Text } from '@radix-ui/themes';
import { BarChartIcon, ArrowRightIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';

export const AdvancedStatsButton: React.FC = () => {
  const router = useRouter();

  const handleClick = () => {
    // Pour l'instant, on peut rediriger vers une page de statistiques ou afficher un message
    console.log('Navigation vers les statistiques avancées');
    // router.push('/dashboard/analytics');
  };

  return (
    <Card 
      size="3" 
      style={{ 
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        border: '1px solid var(--gray-6)'
      }}
      onClick={handleClick}
      className="hover:shadow-md hover:border-blue-7"
    >
      <Flex direction="column" gap="3">
        <Flex align="center" gap="3">
          <Box style={{
            background: 'var(--blue-3)',
            borderRadius: 'var(--radius-2)',
            padding: 'var(--space-2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <BarChartIcon color="var(--blue-11)" width={20} height={20} />
          </Box>
          <Box style={{ flexGrow: 1 }}>
            <Text weight="bold" size="3" mb="2" as="div">
              Analyses Avancées
            </Text>
            <Text size="2" color="gray" as="div">
              Tendances, performance IA, insights détaillés
            </Text>
          </Box>
        </Flex>
        
        <Flex direction="column" gap="2">
          <Text size="1" color="gray" as="div">Disponible prochainement :</Text>
          <Flex direction="column" gap="1">
            <Text size="1" color="gray" as="div">• Évolution des taux de complétion</Text>
            <Text size="1" color="gray" as="div">• Performance par source de données</Text>
            <Text size="1" color="gray" as="div">• Analyses prédictives IA</Text>
          </Flex>
        </Flex>
        
        <Button variant="soft" size="2" style={{ alignSelf: 'flex-start' }}>
          <ArrowRightIcon width={14} height={14} />
          Bientôt disponible
        </Button>
      </Flex>
    </Card>
  );
}; 