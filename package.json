{"name": "diktasis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "date-fns": "^4.1.0", "firebase": "^11.8.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-select": "^5.10.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}