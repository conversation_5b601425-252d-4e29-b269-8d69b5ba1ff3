// import { formatDateFR } from '@/utils/dateUtils'; // Supprimé pour éviter les erreurs d'import

// Types pour les sources connectées et leurs champs
export interface ConnectedDataSource {
  id: string;
  name: string;
  type: string;
  logoSrc: string;
  totalFieldsAvailable: number;
  mappedFieldsCount: number;
  completionRate: number;
  status: string;
  lastSync: string;
}

// Interface pour les entités cibles (entreprises/contacts)
export interface TargetEntity {
  id: string;
  name: string;
  type: 'company' | 'contact';
  sourceId: string;
  avatar?: string;
  details: string;
  completionRate: number;
  lastActivity?: string;
  email?: string;
}

export interface FieldToCollect {
  id: string;
  name: string;
  description: string;
  sourceId: string;
  sourceName: string;
  required: boolean;
  type: 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea';
  options?: string[];
  currentValue?: string;
  completionStatus: 'empty' | 'partial' | 'complete';
  placeholder?: string;
  example?: string;
  rows?: number; 
}

// Templates prédéfinis
export const mockTemplates = [
  {
    id: 'billing-template',
    name: 'Coordonnées de facturation',
    description: 'Collecte les informations légales et d\'adressage nécessaires à la facturation',
    icon: '🧾',
    fieldsCount: 8,
    usageCount: 15,
    lastUsed: '2024-12-18',
    tags: ['Facturation', 'Légal', 'Adresse']
  },
  {
    id: 'contact-template',
    name: 'Informations contact principal',
    description: 'Collecte les coordonnées complètes du contact principal de l\'entreprise',
    icon: '👤',
    fieldsCount: 6,
    usageCount: 23,
    lastUsed: '2024-12-19',
    tags: ['Contact', 'Communication']
  },
  {
    id: 'sales-template',
    name: 'Qualification commerciale',
    description: 'Collecte les informations de qualification pour le processus de vente',
    icon: '💼',
    fieldsCount: 12,
    usageCount: 8,
    lastUsed: '2024-12-17',
    tags: ['Commercial', 'Qualification', 'Budget']
  },
  {
    id: 'linkedin-template',
    name: 'Page LinkedIn Entreprise',
    description: 'Collecte les informations nécessaires pour créer ou optimiser une page LinkedIn d\'entreprise',
    icon: '🔗',
    fieldsCount: 10,
    usageCount: 12,
    lastUsed: '2024-12-19',
    tags: ['LinkedIn', 'Social Media', 'Branding']
  },
  {
    id: 'marketing-template',
    name: 'Campagne Marketing',
    description: 'Collecte les données pour personnaliser et cibler vos campagnes marketing',
    icon: '📢',
    fieldsCount: 9,
    usageCount: 18,
    lastUsed: '2024-12-18',
    tags: ['Marketing', 'Campagne', 'Ciblage']
  },
  {
    id: 'onboarding-template',
    name: 'Onboarding Client',
    description: 'Collecte toutes les informations nécessaires pour l\'intégration d\'un nouveau client',
    icon: '🚀',
    fieldsCount: 15,
    usageCount: 7,
    lastUsed: '2024-12-16',
    tags: ['Onboarding', 'Client', 'Intégration']
  },
  {
    id: 'tech-template',
    name: 'Informations techniques',
    description: 'Collecte les détails techniques et d\'intégration nécessaires au projet',
    icon: '⚙️',
    fieldsCount: 11,
    usageCount: 5,
    lastUsed: '2024-12-16',
    tags: ['Technique', 'Intégration', 'Système']
  },
  {
    id: 'compliance-template',
    name: 'Conformité RGPD',
    description: 'Collecte les informations nécessaires pour la conformité et la protection des données',
    icon: '🛡️',
    fieldsCount: 7,
    usageCount: 9,
    lastUsed: '2024-12-17',
    tags: ['RGPD', 'Conformité', 'Sécurité']
  }
];

// Mock des sources connectées
export const mockConnectedSources: ConnectedDataSource[] = [
  {
    id: 'quickbooks',
    name: 'QuickBooks Online',
    type: 'Facturation',
    status: 'Connecté',
    lastSync: '20/01/2025',
    logoSrc: 'https://cdn.simpleicons.org/quickbooks/0076C5',
    totalFieldsAvailable: 120,
    mappedFieldsCount: 95,
    completionRate: 0.85,
  },
  {
    id: 'odoo',
    name: 'Odoo',
    type: 'ERP/CRM',
    status: 'Connecté',
    lastSync: '20/01/2025',
    logoSrc: 'https://cdn.simpleicons.org/odoo/714B67',
    totalFieldsAvailable: 250,
    mappedFieldsCount: 200,
    completionRate: 0.70,
  }
];

// Mock des entités par source (version réduite pour performance)
export const mockEntitiesBySource: { [sourceId: string]: TargetEntity[] } = {
  odoo: [
    {
      id: 'onyx_trading',
      name: 'Onyx Trading Co. Ltd',
      type: 'company',
      sourceId: 'odoo',
      avatar: '💼',
      details: 'Import-Export International • 15 employés • Secteur Trading',
      completionRate: 0.90,
      lastActivity: '2025-01-10'
    },
    {
      id: 'julien_deprins',
      name: 'Julien Deprins',
      type: 'contact',
      sourceId: 'odoo',
      avatar: '👨‍💻',
      details: 'Sales Manager chez Onyx Trading Co. Ltd',
      completionRate: 0.85,
      lastActivity: '2025-01-09',
      email: '<EMAIL>'
    },
    {
      id: 'innovatech',
      name: 'Innovatech Ltd.',
      type: 'company',
      sourceId: 'odoo',
      avatar: '🏢',
      details: 'Société technologique • 45 employés • Secteur IT',
      completionRate: 0.75,
      lastActivity: '2024-12-19'
    },
    {
      id: 'marie_martin',
      name: 'Marie Martin',
      type: 'contact',
      sourceId: 'odoo',
      avatar: '👩‍💼',
      details: 'Directrice Financière chez Innovatech Ltd.',
      completionRate: 0.70,
      lastActivity: '2025-01-06',
      email: '<EMAIL>'
    },
    {
      id: 'global_logistics',
      name: 'Global Logistics SARL',
      type: 'company',
      sourceId: 'odoo',
      avatar: '🚛',
      details: 'Transport et Logistique • 80 employés • International',
      completionRate: 0.65,
      lastActivity: '2025-01-05'
    },
    {
      id: 'pierre_durand',
      name: 'Pierre Durand',
      type: 'contact',
      sourceId: 'odoo',
      avatar: '👨‍🔧',
      details: 'Responsable Opérations chez Global Logistics SARL',
      completionRate: 0.60,
      lastActivity: '2025-01-04',
      email: '<EMAIL>'
    }
  ],
  quickbooks: [
    {
      id: 'quantum',
      name: 'QuantumLeap Inc.',
      type: 'company',
      sourceId: 'quickbooks',
      avatar: '⚡',
      details: 'Entreprise innovante • 85 employés • Secteur Tech',
      completionRate: 0.60,
      lastActivity: '2024-12-17'
    },
    {
      id: 'sara_lee',
      name: 'Sara Lee',
      type: 'contact',
      sourceId: 'quickbooks',
      avatar: '👩‍💻',
      details: 'Responsable Marketing chez QuantumLeap Inc.',
      completionRate: 0.72,
      lastActivity: '2024-12-19',
      email: '<EMAIL>'
    },
    {
      id: 'synergy_corp',
      name: 'Synergy Corporation',
      type: 'company',
      sourceId: 'quickbooks',
      avatar: '🏢',
      details: 'Entreprise de conseil • 50 employés • Secteur Conseil',
      completionRate: 0.80,
      lastActivity: '2025-01-07'
    },
    {
      id: 'tech_startup',
      name: 'TechStartup Inc.',
      type: 'company',
      sourceId: 'quickbooks',
      avatar: '💻',
      details: 'Développement logiciel • 12 employés • Secteur Tech',
      completionRate: 0.85,
      lastActivity: '2025-01-09'
    },
    {
      id: 'sarah_johnson',
      name: 'Sarah Johnson',
      type: 'contact',
      sourceId: 'quickbooks',
      avatar: '👩‍💻',
      details: 'CEO chez TechStartup Inc.',
      completionRate: 0.90,
      lastActivity: '2025-01-08',
      email: '<EMAIL>'
    }
  ]
};
