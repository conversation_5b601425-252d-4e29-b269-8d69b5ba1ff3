'use client';

import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  Flex,
  Text,
  Heading,
  Box,
  Tabs,
  Tooltip,
  Spinner
} from '@radix-ui/themes';
import { PlusCircledIcon, InfoCircledIcon, TableIcon, GridIcon, UpdateIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { SmartFormsStats } from '../../../components/smartforms/SmartFormsStats';
import { SmartFormsFilters } from '../../../components/smartforms/SmartFormsFilters';
import { SmartFormsTable } from '../../../components/smartforms/SmartFormsTable';
import { useSmartForms } from '../../../hooks/useSmartForms';
import { useRealtimeUpdates } from '../../../hooks/useSSE';
import { getErrorMessage } from '../../../types/errors';

export default function MySmartFormsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  // Activer les mises à jour en temps réel via SSE
  useRealtimeUpdates();

  // Utilisation du hook React Query
  const {
    data: allForms = [],
    isLoading,
    error,
    refetch
  } = useSmartForms(searchTerm);

  const handleRefresh = () => {
    refetch();
  };

  const filteredAndSortedForms = useMemo(() => {
    return allForms
      .filter(form => form.name.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }, [allForms, searchTerm]);

  if (isLoading) {
    return (
      <Flex direction="column" align="center" justify="center" gap="3" style={{ minHeight: '300px' }}>
        <Spinner size="3" />
        <Text>Chargement des SmartForms...</Text>
      </Flex>
    );
  }

  if (error) {
    return (
      <Flex direction="column" align="center" justify="center" gap="3" style={{ minHeight: '300px' }}>
        <Text color="tomato">Erreur: {getErrorMessage(error)}</Text>
        <Button onClick={handleRefresh}><UpdateIcon /> Réessayer</Button>
      </Flex>
    );
  }

  return (
    <Box p={{ initial: '4', md: '6' }}>
      <Flex justify="between" align="center" mb="6" wrap={{ initial: 'wrap', xs: 'nowrap' }} gap="3">
        <Heading as="h1" size="7">Mes SmartForms</Heading>
        <Button onClick={() => router.push('/dashboard/new-smart-forms')} size="3">
          <PlusCircledIcon /> Nouveau SmartForm
        </Button>
      </Flex>
      
      <SmartFormsStats smartForms={filteredAndSortedForms} />
      
      <Tabs.Root defaultValue="list" mb="6">
        <Tabs.List>
          <Tabs.Trigger value="list"><TableIcon /> Liste</Tabs.Trigger>
          <Tabs.Trigger value="grid" disabled><GridIcon /> Grille (bientôt)</Tabs.Trigger>
          <Button onClick={handleRefresh} variant="soft" color="gray" title="Rafraîchir la liste des formulaires" style={{ marginLeft: 'auto' }}>
            <UpdateIcon />
          </Button>
        </Tabs.List>
        <Tabs.Content value="list">
          <Box pt="4">
            <SmartFormsTable smartForms={filteredAndSortedForms} />
          </Box>
        </Tabs.Content>
        <Tabs.Content value="grid">
          <Box pt="4">
            <Text>La vue en grille sera bientôt disponible.</Text>
          </Box>
        </Tabs.Content>
      </Tabs.Root>
    </Box>
  );
} 