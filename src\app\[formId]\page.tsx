'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { 
    <PERSON><PERSON>, 
    Card, 
    Container, 
    Flex, 
    Heading, 
    Text, 
    Spinner,
    Callout
} from '@radix-ui/themes';
import { FormConfig, FormField } from '@/types/form'; // Utiliser les types partagés
import PublicFormRenderer from '@/components/form/PublicFormRenderer'; // Importer le renderer partagé
import { AlertTriangleIcon, CheckCircle2Icon } from 'lucide-react'; // Pour des icônes de succès/erreur plus distinctes

export default function PublicSmartFormPage() {
  const params = useParams();
  const formId = params.formId as string;

  const [formConfig, setFormConfig] = useState<FormConfig | null>(null);
  const [formData, setFormData] = useState<{ [key: string]: any }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true); // S'assurer que le code client s'exécute après le montage
  }, []);

  // Déplacer fetchFormConfig ici et utiliser useCallback
  const fetchFormConfig = useCallback(async () => {
    if (!formId || !isClient) return; // Ne pas fetcher si pas d'ID ou pas encore côté client

    setIsLoading(true);
    setError(null);
    setSubmissionSuccess(false);
    try {
      let config: FormConfig | null = null;
      if (typeof window !== 'undefined') {
        const storedConfigData = localStorage.getItem(`formConfig-${formId}`);
        if (storedConfigData) {
          console.log('Chargement de la configuration depuis localStorage pour:', formId);
          try {
            config = JSON.parse(storedConfigData);
            if (!config || !config.fields || !config.title) {
                console.warn('Config localStorage invalide pour:', formId, 'tentative via API.');
                config = null; 
                localStorage.removeItem(`formConfig-${formId}`); 
            }
          } catch (e) {
            console.warn('Erreur parsing config localStorage pour:', formId, 'tentative via API.', e);
            config = null; 
            localStorage.removeItem(`formConfig-${formId}`); 
          }
        }
      }

      if (!config) {
        console.log('Chargement de la configuration depuis API pour:', formId);
        const response = await fetch(`/api/smart-forms/${formId}`);
        if (!response.ok) {
          if (response.status === 404) throw new Error('Formulaire non trouvé (404).');
          throw new Error(`Erreur lors du chargement de la configuration (HTTP ${response.status}).`);
        }
        config = await response.json() as FormConfig; 
      }
      
      if (!config || !config.fields || typeof config.title === 'undefined') { 
         throw new Error('Configuration de formulaire reçue invalide ou vide.');
      }

      setFormConfig({...config, status: config.status === 'preview' ? 'preview' : 'editable' }); // Conserver le statut preview si présent

      const initialData: { [key: string]: any } = {};
      config.fields.forEach(field => {
        initialData[field.id] = field.defaultValue !== undefined ? field.defaultValue :
                               field.type === 'checkbox' ? false : '';
      });
      setFormData(initialData);

    } catch (err: any) {
      setError(err.message || 'Impossible de charger la configuration du formulaire.');
      console.error("Erreur fetchFormConfig:",err);
      setFormConfig(null);
    } finally {
      setIsLoading(false);
    }
  }, [formId, isClient]);

  useEffect(() => {
    if (formId && isClient) {
      fetchFormConfig(); // Appeler la fonction définie avec useCallback
    }
  }, [formId, isClient, fetchFormConfig]); // fetchFormConfig est maintenant une dépendance stable

  const handleInputChange = (fieldId: string, value: any) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldId]: value,
    }));
    if (error) setError(null); // Effacer les erreurs de validation précédentes lors de la saisie
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!formConfig) return;

    setIsSubmitting(true);
    setError(null);

    for (const field of formConfig.fields) {
      if (field.required && 
         (formData[field.id] === undefined || formData[field.id] === '' || 
         (field.type === 'checkbox' && formData[field.id] === false))) {
        setError(`Le champ "${field.label || field.name}" est requis.`);
        setIsSubmitting(false);
        return;
      }
    }
    
    console.log('Soumission des données:', formData, 'pour le formulaire:', formId);

    try {
      // Utiliser formConfig.submitUrl si défini, sinon la route API par défaut
      const submitUrl = formConfig.submitUrl && formConfig.submitUrl !== '#' 
                        ? formConfig.submitUrl 
                        : `/api/smart-forms/${formId}`;

      const response = await fetch(submitUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        let errorMessage = 'Erreur lors de la soumission du formulaire.';
        try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) { errorMessage = `${errorMessage} Statut: ${response.status}`; }
        throw new Error(errorMessage);
      }

      await response.json(); 
      setSubmissionSuccess(true);

      // Vérifier si tous les champs requis sont remplis pour marquer comme "complété"
      let allRequiredFilled = true;
      for (const field of formConfig.fields) {
        if (field.required && 
           (formData[field.id] === undefined || formData[field.id] === '' || 
           (field.type === 'checkbox' && formData[field.id] === false))) {
          allRequiredFilled = false;
          break;
        }
      }

      if (allRequiredFilled) {
        console.log('Tous les champs requis sont remplis, mise à jour du statut en "completed"');
        if (typeof window !== 'undefined') {
          const storedConfigData = localStorage.getItem(`formConfig-${formId}`);
          if (storedConfigData) {
            try {
              const currentStoredConfig = JSON.parse(storedConfigData);
              currentStoredConfig.status = 'completed';
              localStorage.setItem(`formConfig-${formId}`, JSON.stringify(currentStoredConfig));
              console.log('Statut mis à jour dans localStorage pour:', formId);
            } catch (e) {
              console.error('Erreur lors de la mise à jour du statut dans localStorage:', e);
            }
          }
        }
      }

      if (typeof window !== 'undefined') window.scrollTo(0, 0);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la soumission.');
      console.error("Erreur handleSubmit:", err);
      if (typeof window !== 'undefined') window.scrollTo(0, 0);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !isClient) {
    return (
      <Container size="2" py="8" style={{ textAlign: 'center' }}>
        <Spinner size="3" />
        <Text as="div" mt="4">Chargement du formulaire...</Text>
      </Container>
    );
  }
  
  if (submissionSuccess) {
    return (
      <Container size="2" py="8">
        <Card style={{ textAlign: 'center' }}>
          <Flex direction="column" gap="3" align="center" p="4">
            <Callout.Root color="green" size="2">
                <Callout.Icon><CheckCircle2Icon /></Callout.Icon>
                <Callout.Text>{formConfig?.successMessage || "Formulaire soumis avec succès !"}</Callout.Text>
            </Callout.Root>
            <Text mt="3">Merci, vos informations ont bien été enregistrées.</Text>
            <Text>Vous pouvez fermer cette page.</Text>
          </Flex>
        </Card>
      </Container>
    );
  }

  // Afficher l'erreur principale si elle existe et qu'il n'y a pas de config de formulaire
  // Les erreurs de validation spécifiques aux champs sont gérées dans PublicFormRenderer (via submissionError prop)
  if (error && !formConfig) {
    return (
      <Container size="2" py="8">
        <Card style={{ textAlign: 'center'}}>
          <Flex direction="column" gap="3" align="center" p="4">
            <Callout.Root color="red" size="2">
                <Callout.Icon><AlertTriangleIcon /></Callout.Icon>
                <Callout.Text>{error}</Callout.Text>
            </Callout.Root>
            <Button onClick={fetchFormConfig} mt="3">Réessayer de charger</Button>
          </Flex>
        </Card>
      </Container>
    );
  }
  
  if (!formConfig) { 
      return (
          <Container size="2" py="8" style={{ textAlign: 'center' }}>
              <Text color="gray">Aucune configuration de formulaire trouvée ou erreur de chargement.</Text>
          </Container>
      );
  }

  // Le titre et la description sont maintenant gérés dans la Card principale
  return (
    <Container 
      size="3"
      py={{ initial: "var(--space-5)", sm: "var(--space-7)" }}
      style={{ minHeight: '100vh', fontFamily: 'var(--default-font-family)'}}
    >
      <Card style={{ maxWidth: '700px', margin: '0 auto', padding: 'var(--space-5) var(--space-6)' }}>
        <Heading align="center" size="7" mb="2">{formConfig.title}</Heading>
        {formConfig.description && (
          <Text as="p" size="3" color="gray" align="center" mb="6" style={{ whiteSpace: 'pre-wrap'}}>
            {formConfig.description}
          </Text>
        )}
        {formConfig.deadline && (
            <Callout.Root color="orange" variant='outline' mb="5">
                <Callout.Text>
                    Ce formulaire est à compléter avant le <Text weight="bold">{new Date(formConfig.deadline  + 'T00:00:00').toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })}</Text>.
                </Callout.Text>
            </Callout.Root>
        )}

        <PublicFormRenderer 
          formConfig={formConfig} 
          formData={formData} 
          onInputChange={handleInputChange} 
          onSubmit={handleSubmit} 
          isSubmitting={isSubmitting} 
          submissionError={error} // Passer l'état d'erreur général comme submissionError
        />
      </Card>
      <Text size="1" color="gray" align="center" mt="6">SmartForm ID: {formId}</Text>
    </Container>
  );
} 