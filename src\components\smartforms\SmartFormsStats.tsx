import React from 'react';
import { <PERSON>, Card, Flex, Grid, Heading, Text } from '@radix-ui/themes';
import { 
  FileTextIcon, 
  PaperPlaneIcon, 
  CheckCircledIcon, 
  ClockIcon,
  ExclamationTriangleIcon 
} from '@radix-ui/react-icons';
import { SmartForm } from '../../types/dashboard';

interface SmartFormsStatsProps {
  smartForms: SmartForm[];
}

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactElement;
  color: "blue" | "green" | "yellow" | "purple" | "gray";
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, description }) => {
  return (
    <Card size="2">
      <Flex gap="3" align="center">
        <Flex 
          align="center" 
          justify="center" 
          style={{
            width: 40, 
            height: 40, 
            borderRadius: 'var(--radius-2)', 
            backgroundColor: `var(--${color}-a3)`,
          }}
        >
          <Box style={{ color: `var(--${color}-11)` }}>
            {icon}
          </Box>
        </Flex>
        <Box>
          <Text size="2" color="gray" as="div">{title}</Text>
          <Heading as="h3" size="6" weight="bold" style={{ color: `var(--${color}-11)` }}>
            {value}
          </Heading>
          {description && (
            <Text size="1" color="gray" as="div" style={{ marginTop: '2px' }}>
              {description}
            </Text>
          )}
        </Box>
      </Flex>
    </Card>
  );
};

export const SmartFormsStats: React.FC<SmartFormsStatsProps> = ({ smartForms }) => {
  const totalForms = smartForms.length;
  const draftForms = smartForms.filter(f => f.status === 'draft').length;
  const sentForms = smartForms.filter(f => f.status === 'sent').length;
  const completedForms = smartForms.filter(f => f.status === 'completed').length;
  
  // Calculer les formulaires en attente (envoyés sans réponse complétée)
  const pendingForms = smartForms.filter(f => 
    f.status === 'sent' && 
    (!f.responses || f.responses.every(r => r.status === 'pending'))
  ).length;

  const stats: StatCardProps[] = [
    {
      title: 'Total',
      value: totalForms,
      icon: <FileTextIcon />,
      color: 'blue',
      description: 'Tous les formulaires'
    },
    {
      title: 'Brouillons',
      value: draftForms,
      icon: <ExclamationTriangleIcon />,
      color: 'yellow',
      description: 'Non envoyés'
    },
    {
      title: 'En Attente',
      value: pendingForms,
      icon: <ClockIcon />,
      color: 'purple',
      description: 'Attendent une réponse'
    },
    {
      title: 'Complétés',
      value: completedForms,
      icon: <CheckCircledIcon />,
      color: 'green',
      description: 'Réponses reçues'
    }
  ];

  return (
    <Box mb="6">
      <Heading as="h2" size="5" weight="bold" mb="4">
        Vue d'ensemble
      </Heading>
      <Grid columns={{ initial: "2", md: "4" }} gap="4">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </Grid>
    </Box>
  );
}; 