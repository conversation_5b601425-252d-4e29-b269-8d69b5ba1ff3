'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { 
  Button, 
  Flex, 
  Text, 
  Heading, 
  Card, 
  Badge, 
  Box,
  Progress,
  Separator
} from '@radix-ui/themes';
import { 
  CheckIcon,
  ChevronLeftIcon,
  ReloadIcon,
  ExternalLinkIcon,
  UpdateIcon
} from '@radix-ui/react-icons';

// Types pour la synchronisation
interface SyncStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  duration?: number;
  details?: string;
}

interface CRMSyncData {
  formId: string;
  clientName: string;
  crmSystem: string;
  syncSteps: SyncStep[];
  overallStatus: 'pending' | 'running' | 'completed' | 'error';
  startedAt?: string;
  completedAt?: string;
}

// Données mockées
const getCRMSyncData = (formId: string): CRMSyncData => {
  return {
    formId,
    clientName: 'Innovatech Ltd.',
    crmSystem: 'Odoo',
    overallStatus: 'pending',
    syncSteps: [
      {
        id: 'validation',
        name: 'Validation des données',
        description: 'Vérification de la cohérence et de la validité des données',
        status: 'pending'
      },
      {
        id: 'mapping',
        name: 'Mapping des champs',
        description: 'Association des champs SmartForm avec les champs CRM',
        status: 'pending'
      },
      {
        id: 'contact_update',
        name: 'Mise à jour du contact',
        description: 'Synchronisation des informations de contact',
        status: 'pending'
      },
      {
        id: 'company_update',
        name: 'Mise à jour de l\'entreprise',
        description: 'Synchronisation des informations de l\'entreprise',
        status: 'pending'
      },
      {
        id: 'documents',
        name: 'Gestion des documents',
        description: 'Archivage et indexation des documents',
        status: 'pending'
      },
      {
        id: 'notification',
        name: 'Notifications',
        description: 'Envoi des notifications de confirmation',
        status: 'pending'
      }
    ]
  };
};

export default function CRMSyncPage() {
  const params = useParams();
  const router = useRouter();
  const formId = params.id as string;
  
  const [syncData, setSyncData] = useState<CRMSyncData | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    const data = getCRMSyncData(formId);
    setSyncData(data);
  }, [formId]);

  const startSync = async () => {
    if (!syncData) return;
    
    setIsRunning(true);
    setSyncData(prev => prev ? {
      ...prev,
      overallStatus: 'running',
      startedAt: new Date().toISOString()
    } : null);

    // Simuler l'exécution des étapes
    for (let i = 0; i < syncData.syncSteps.length; i++) {
      const step = syncData.syncSteps[i];
      
      // Marquer l'étape comme en cours
      setSyncData(prev => prev ? {
        ...prev,
        syncSteps: prev.syncSteps.map(s => 
          s.id === step.id ? { ...s, status: 'running' } : s
        )
      } : null);

      // Simuler le temps d'exécution
      const duration = Math.random() * 2000 + 1000; // 1-3 secondes
      await new Promise(resolve => setTimeout(resolve, duration));

      // Marquer l'étape comme terminée
      setSyncData(prev => prev ? {
        ...prev,
        syncSteps: prev.syncSteps.map(s => 
          s.id === step.id ? { 
            ...s, 
            status: 'completed', 
            duration: Math.round(duration),
            details: getStepDetails(step.id)
          } : s
        )
      } : null);
    }

    // Marquer la synchronisation comme terminée
    setSyncData(prev => prev ? {
      ...prev,
      overallStatus: 'completed',
      completedAt: new Date().toISOString()
    } : null);
    
    setIsRunning(false);
  };

  const getStepDetails = (stepId: string): string => {
    switch (stepId) {
      case 'validation':
        return '12 champs validés, 0 erreur détectée';
      case 'mapping':
        return '12 champs mappés vers Odoo';
      case 'contact_update':
        return 'Contact "Jean Dupont" mis à jour';
      case 'company_update':
        return 'Entreprise "Innovatech Ltd." mise à jour';
      case 'documents':
        return '1 document archivé dans Odoo';
      case 'notification':
        return 'Email de confirmation envoyé';
      default:
        return 'Étape terminée avec succès';
    }
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckIcon width={16} height={16} color="green" />;
      case 'running':
        return <ReloadIcon width={16} height={16} color="blue" className="animate-spin" />;
      case 'error':
        return <ExternalLinkIcon width={16} height={16} color="red" />;
      default:
        return <div style={{ width: 16, height: 16, backgroundColor: 'var(--gray-6)', borderRadius: '50%' }} />;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  if (!syncData) {
    return (
      <Box style={{ maxWidth: 'var(--max-width-4xl)', margin: '0 auto' }} py="8">
        <Card>
          <Box p="6">
            <Text>Chargement des données de synchronisation...</Text>
          </Box>
        </Card>
      </Box>
    );
  }

  const completedSteps = syncData.syncSteps.filter(s => s.status === 'completed').length;
  const totalSteps = syncData.syncSteps.length;
  const progress = (completedSteps / totalSteps) * 100;

  return (
    <Box style={{ maxWidth: 'var(--max-width-4xl)', margin: '0 auto' }} py="8">
      {/* En-tête */}
      <Flex justify="between" align="center" mb="6">
        <Box>
          <Heading as="h1" size="8" weight="bold" mb="1">
            Synchronisation CRM
          </Heading>
          <Text color="gray" size="4">
            Intégration des données validées dans votre système CRM
          </Text>
        </Box>
        <Button 
          variant="ghost" 
          onClick={() => router.push(`/dashboard/smart-forms/${formId}`)}
        >
          <ChevronLeftIcon width={16} height={16} />
          Retour
        </Button>
      </Flex>

      {/* Informations générales */}
      <Card mb="6">
        <Box p="6">
          <Flex justify="between" align="center" mb="4">
            <Box>
              <Heading size="6" mb="2">
                Synchronisation avec {syncData.crmSystem}
              </Heading>
              <Text size="3" color="gray">
                Client : {syncData.clientName} • SmartForm ID : {syncData.formId}
              </Text>
            </Box>
            <Badge 
              color={syncData.overallStatus === 'completed' ? 'green' : syncData.overallStatus === 'running' ? 'blue' : 'gray'} 
              variant="soft" 
              size="2"
            >
              {syncData.overallStatus === 'completed' ? 'Terminé' : 
               syncData.overallStatus === 'running' ? 'En cours' : 'En attente'}
            </Badge>
          </Flex>

          {syncData.overallStatus === 'pending' && (
            <Button
              size="3"
              variant="solid"
              color="blue"
              onClick={startSync}
              disabled={isRunning}
            >
              <UpdateIcon width={16} height={16} />
              Démarrer la synchronisation
            </Button>
          )}

          {syncData.overallStatus === 'running' && (
            <Box>
              <Flex justify="between" align="center" mb="2">
                <Text size="3" weight="medium">Progression</Text>
                <Text size="2" color="gray">
                  {completedSteps} / {totalSteps} étapes
                </Text>
              </Flex>
              <Progress value={progress} size="3" />
            </Box>
          )}

          {syncData.overallStatus === 'completed' && syncData.startedAt && syncData.completedAt && (
            <Box>
              <Text size="2" color="green" weight="medium" mb="2">
                ✅ Synchronisation terminée avec succès
              </Text>
              <Text size="2" color="gray">
                Durée : {Math.round((new Date(syncData.completedAt).getTime() - new Date(syncData.startedAt).getTime()) / 1000)} secondes
              </Text>
            </Box>
          )}
        </Box>
      </Card>

      {/* Étapes de synchronisation */}
      <Card>
        <Box p="6">
          <Heading size="5" mb="4">
            Étapes de synchronisation
          </Heading>
          
          <Flex direction="column" gap="4">
            {syncData.syncSteps.map((step, index) => (
              <Box key={step.id}>
                <Card variant="surface">
                  <Box p="4">
                    <Flex align="center" gap="3">
                      {getStepIcon(step.status)}
                      <Box style={{ flexGrow: 1 }}>
                        <Flex justify="between" align="center" mb="1">
                          <Text size="3" weight="medium">
                            {step.name}
                          </Text>
                          <Badge color={getStepColor(step.status)} variant="soft" size="1">
                            {step.status === 'completed' ? 'Terminé' :
                             step.status === 'running' ? 'En cours' :
                             step.status === 'error' ? 'Erreur' : 'En attente'}
                          </Badge>
                        </Flex>
                        <Text size="2" color="gray" mb="2">
                          {step.description}
                        </Text>
                        {step.details && (
                          <Text size="2" color="green">
                            {step.details}
                          </Text>
                        )}
                        {step.duration && (
                          <Text size="1" color="gray" mt="1">
                            Durée : {step.duration}ms
                          </Text>
                        )}
                      </Box>
                    </Flex>
                  </Box>
                </Card>
                
                {index < syncData.syncSteps.length - 1 && (
                  <Box style={{ 
                    width: '2px', 
                    height: '20px', 
                    backgroundColor: step.status === 'completed' ? 'var(--green-9)' : 'var(--gray-6)',
                    margin: '8px 0 8px 8px'
                  }} />
                )}
              </Box>
            ))}
          </Flex>

          {syncData.overallStatus === 'completed' && (
            <Box mt="6">
              <Separator size="4" mb="4" />
              <Flex gap="3">
                <Button
                  size="3"
                  variant="soft"
                  color="blue"
                  onClick={() => window.open('https://odoo.com', '_blank')}
                >
                  <ExternalLinkIcon width={16} height={16} />
                  Ouvrir {syncData.crmSystem}
                </Button>
                <Button
                  size="3"
                  variant="outline"
                  color="gray"
                  onClick={() => router.push('/dashboard/smart-forms')}
                >
                  Retour aux SmartForms
                </Button>
              </Flex>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
} 