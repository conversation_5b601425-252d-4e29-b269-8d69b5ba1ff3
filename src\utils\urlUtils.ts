/**
 * Utilitaires pour la gestion des URLs
 */

/**
 * Obtient l'URL de base de l'application
 * Fonctionne côté client et serveur, s'adapte automatiquement au port
 */
export function getBaseUrl(): string {
  // Côté client : utiliser window.location.origin (inclut protocole, domaine et port)
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Côté serveur : utiliser les variables d'environnement
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL;
  }

  // Fallback pour le développement
  const port = process.env.PORT || '3000';
  return `http://localhost:${port}`;
}

/**
 * Génère une URL complète pour un formulaire public
 */
export function generateFormUrl(formId: string): string {
  const baseUrl = getBaseUrl();
  return `${baseUrl}/${formId}`;
}

/**
 * Vérifie si une URL est valide
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}
