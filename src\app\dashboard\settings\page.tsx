'use client'; // S'assurer que c'est un Client Component
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext'; // Importer useAuth
import { Button, TextField, Heading, Text, Flex, Avatar, Box, Card, Separator, Switch, Badge, Callout } from '@radix-ui/themes'; // Importer des composants Radix UI
import { InfoCircledIcon, CheckCircledIcon, ExclamationTriangleIcon } from '@radix-ui/react-icons';
import { formatDateFR, formatDateTimeFR } from '@/utils/dateUtils';

export default function SettingsPage() {
  const { user, loading } = useAuth();

  // États pour les champs du formulaire, initialisés avec les données de l'utilisateur
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [organization, setOrganization] = useState('');
  
  // États pour les notifications
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [productUpdates, setProductUpdates] = useState(false);
  const [securityAlerts, setSecurityAlerts] = useState(true);
  
  // États pour la gestion des messages
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  
  // État pour les modifications non sauvegardées
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Fonction pour déterminer le type de compte
  const getAccountType = () => {
    if (!user || !user.providerData) return 'Inconnu';
    
    const providers = user.providerData.map(provider => provider.providerId);
    
    if (providers.includes('google.com')) {
      return 'Google';
    } else if (providers.includes('password')) {
      return 'Email/Mot de passe';
    } else if (providers.includes('facebook.com')) {
      return 'Facebook';
    } else if (providers.includes('twitter.com')) {
      return 'Twitter';
    } else if (providers.includes('github.com')) {
      return 'GitHub';
    } else if (providers.includes('microsoft.com')) {
      return 'Microsoft';
    } else if (providers.includes('apple.com')) {
      return 'Apple';
    } else if (providers.includes('phone')) {
      return 'Téléphone';
    } else {
      return 'Autre';
    }
  };

  // Fonction pour obtenir la couleur du badge selon le type de compte
  const getAccountTypeBadgeColor = () => {
    const accountType = getAccountType();
    switch (accountType) {
      case 'Google':
        return 'blue';
      case 'Email/Mot de passe':
        return 'green';
      case 'Facebook':
        return 'indigo';
      case 'Twitter':
        return 'cyan';
      case 'GitHub':
        return 'gray';
      case 'Microsoft':
        return 'orange';
      case 'Apple':
        return 'gray';
      case 'Téléphone':
        return 'purple';
      default:
        return 'gray';
    }
  };

  // Fonction pour formater la date de création du compte
  const getAccountCreationDate = () => {
    if (!user?.metadata?.creationTime) return 'Non disponible';
    return formatDateFR(user.metadata.creationTime);
  };

  // Fonction pour formater la dernière connexion
  const getLastSignInDate = () => {
    if (!user?.metadata?.lastSignInTime) return 'Non disponible';
    return formatDateTimeFR(user.metadata.lastSignInTime);
  };

  useEffect(() => {
    if (user) {
      const nameParts = user.displayName?.split(' ') || [];
      setFirstName(nameParts[0] || '');
      setLastName(nameParts.slice(1).join(' ') || '');
      setDisplayName(user.displayName || '');
      setEmail(user.email || '');
    }
  }, [user]); // Mettre à jour les champs lorsque l'objet user change

  // Détecter les modifications non sauvegardées
  useEffect(() => {
    if (user) {
      const originalDisplayName = user.displayName || '';
      const currentDisplayName = `${firstName} ${lastName}`.trim();
      
      const hasChanges = 
        displayName !== originalDisplayName ||
        currentDisplayName !== originalDisplayName ||
        organization !== '';
      
      setHasUnsavedChanges(hasChanges);
    }
  }, [firstName, lastName, displayName, organization, user]);

  const handleSaveChanges = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaveStatus('saving');
    setErrorMessage('');
    
    try {
    // TODO: Implémenter la logique de sauvegarde des modifications
      // Simuler une sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveStatus('success');
      setHasUnsavedChanges(false);
      
      // Réinitialiser le statut après 3 secondes
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      setErrorMessage('Erreur lors de la sauvegarde. Veuillez réessayer.');
    }
  };

  const handleCancel = () => {
    if (user) {
      const nameParts = user.displayName?.split(' ') || [];
      setFirstName(nameParts[0] || '');
      setLastName(nameParts.slice(1).join(' ') || '');
      setDisplayName(user.displayName || '');
      setEmail(user.email || '');
      setOrganization('');
      setHasUnsavedChanges(false);
    }
  };

  if (loading) {
    return (
      <Box maxWidth="800px" mx="auto" p="4">
        <Text>Chargement des paramètres...</Text>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box maxWidth="800px" mx="auto" p="4">
        <Callout.Root color="red">
          <Callout.Icon>
            <ExclamationTriangleIcon />
          </Callout.Icon>
          <Callout.Text>
            Veuillez vous connecter pour accéder aux paramètres.
          </Callout.Text>
        </Callout.Root>
      </Box>
    );
  }
  
  return (
    <Box maxWidth="800px" mx="auto" p="4">
      <Heading as="h1" size="7" weight="bold" mb="6">Paramètres du compte</Heading>

      {/* Messages de statut */}
      {saveStatus === 'success' && (
        <Callout.Root color="green" mb="4">
          <Callout.Icon>
            <CheckCircledIcon />
          </Callout.Icon>
          <Callout.Text>
            Modifications sauvegardées avec succès !
          </Callout.Text>
        </Callout.Root>
      )}

      {saveStatus === 'error' && (
        <Callout.Root color="red" mb="4">
          <Callout.Icon>
            <ExclamationTriangleIcon />
          </Callout.Icon>
          <Callout.Text>
            {errorMessage}
          </Callout.Text>
        </Callout.Root>
      )}

      {/* Section Profil */}
      <Card size="3" mb="6">
        <Heading as="h2" size="5" weight="medium" mb="4">Profil</Heading>
        
        {/* Affichage du type de compte */}
        <Box mb="4">
          <Flex align="center" gap="2">
            <Text size="2" weight="medium" color="gray">Type de compte :</Text>
            <Badge color={getAccountTypeBadgeColor()} variant="soft" size="2">
              {getAccountType()}
            </Badge>
          </Flex>
        </Box>

        <form onSubmit={handleSaveChanges}>
          <Flex direction="column" gap="4">
            <Flex align="center" gap="4" mb="2">
              <Avatar
                src={user.photoURL || undefined}
                fallback={displayName ? displayName[0].toUpperCase() : 'U'}
                size="5" 
                radius="full"
              />
              <Button variant="outline" size="2">Changer la photo</Button>
            </Flex>

            <Flex direction={{ initial: 'column', sm: 'row' }} gap="4">
              <Box style={{ flexGrow: 1}}>
                <label htmlFor="firstName" style={{ display: 'block', marginBottom: 'var(--space-1)'}}><Text size="2" weight="medium">Prénom</Text></label>
                <TextField.Root 
                  id="firstName"
                  type="text" 
                  value={firstName} 
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Votre prénom"
                  size="3"
                />
              </Box>
              <Box style={{ flexGrow: 1}}>
                <label htmlFor="lastName" style={{ display: 'block', marginBottom: 'var(--space-1)'}}><Text size="2" weight="medium">Nom</Text></label>
                <TextField.Root 
                  id="lastName"
                  type="text" 
                  value={lastName} 
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Votre nom"
                  size="3"
                />
              </Box>
            </Flex>
            
            <Box>
              <label htmlFor="displayName" style={{ display: 'block', marginBottom: 'var(--space-1)'}}><Text size="2" weight="medium">Nom d'affichage</Text></label>
              <TextField.Root 
                id="displayName"
                type="text" 
                value={displayName} 
                onChange={(e) => setDisplayName(e.target.value)} 
                placeholder="Nom complet affiché"
                size="3"
              />
            </Box>

            <Box>
              <label htmlFor="email" style={{ display: 'block', marginBottom: 'var(--space-1)'}}><Text size="2" weight="medium">Adresse e-mail</Text></label>
              <TextField.Root 
                id="email"
                type="email" 
                value={email} 
                onChange={(e) => setEmail(e.target.value)} 
                placeholder="Votre adresse e-mail"
                size="3"
                disabled
              />
              <Text size="1" color="gray" mt="1">
                {getAccountType() === 'Google' 
                  ? "L'email ne peut pas être modifié pour les comptes créés avec l'authentification Google."
                  : "L'email ne peut pas être modifié directement. Contactez le support si nécessaire."
                }
              </Text>
            </Box>
            
            <Box>
              <label htmlFor="organization" style={{ display: 'block', marginBottom: 'var(--space-1)'}}><Text size="2" weight="medium">Organisation (facultatif)</Text></label>
              <TextField.Root 
                id="organization"
                type="text" 
                value={organization} 
                onChange={(e) => setOrganization(e.target.value)} 
                placeholder="Nom de votre entreprise"
                size="3"
              />
            </Box>

            <Separator my="3" size="4" />

            <Flex justify="end" gap="3">
              <Button 
                variant="outline" 
                color="gray" 
                size="3" 
                onClick={handleCancel}
                disabled={!hasUnsavedChanges || saveStatus === 'saving'}
              >
                Annuler
              </Button>
              <Button 
                type="submit" 
                variant="solid" 
                color="blue" 
                size="3"
                disabled={!hasUnsavedChanges || saveStatus === 'saving'}
                loading={saveStatus === 'saving'}
              >
                {saveStatus === 'saving' ? 'Sauvegarde...' : 'Enregistrer les modifications'}
              </Button>
            </Flex>
          </Flex>
        </form>
      </Card>

      {/* Section Notifications */}
      <Card size="3" mb="6">
        <Heading as="h2" size="5" weight="medium" mb="4">Notifications</Heading>
        <Flex direction="column" gap="4">
          <Flex justify="between" align="center">
            <Flex direction="column" gap="1">
              <Text size="3" weight="medium">E-mails de notification</Text>
              <Text size="2" color="gray">Recevoir des notifications par e-mail pour les activités importantes</Text>
            </Flex>
            <Switch 
              checked={emailNotifications}
              onCheckedChange={setEmailNotifications}
            />
          </Flex>
          <Flex justify="between" align="center">
            <Flex direction="column" gap="1">
              <Text size="3" weight="medium">Mises à jour produit</Text>
              <Text size="2" color="gray">Recevoir des informations sur les nouvelles fonctionnalités</Text>
            </Flex>
            <Switch 
              checked={productUpdates}
              onCheckedChange={setProductUpdates}
            />
          </Flex>
          <Flex justify="between" align="center">
            <Flex direction="column" gap="1">
              <Text size="3" weight="medium">Alertes de sécurité</Text>
              <Text size="2" color="gray">Recevoir des alertes en cas d'activité suspecte</Text>
            </Flex>
            <Switch 
              checked={securityAlerts}
              onCheckedChange={setSecurityAlerts}
            />
          </Flex>
        </Flex>
      </Card>

      {/* Section Sécurité */}
      <Card size="3" mb="6">
        <Heading as="h2" size="5" weight="medium" mb="4">Sécurité</Heading>
        <Flex direction="column" gap="4">
          {getAccountType() === 'Email/Mot de passe' && (
            <Flex direction="column" gap="2">
              <Text size="3" weight="medium">Mot de passe</Text>
              <Text size="2" color="gray">
                Modifiez votre mot de passe pour maintenir la sécurité de votre compte
              </Text>
              <Box>
                <Button variant="outline" color="orange" size="2">
                  Changer le mot de passe
                </Button>
              </Box>
            </Flex>
          )}
          
          <Flex justify="between" align="start">
            <Flex direction="column" gap="1">
              <Text size="3" weight="medium">Sessions actives</Text>
              <Text size="2" color="gray">
                Gérez les appareils connectés à votre compte
              </Text>
            </Flex>
            <Button variant="outline" size="2">
              Voir les sessions actives
            </Button>
          </Flex>
          
          <Flex direction="column" gap="2">
            <Text size="3" weight="medium">Authentification à deux facteurs</Text>
            <Text size="2" color="gray">
              Ajoutez une couche de sécurité supplémentaire à votre compte
            </Text>
            <Box>
              <Button variant="outline" size="2">
                Configurer 2FA
              </Button>
            </Box>
          </Flex>
        </Flex>
      </Card>

      {/* Section Informations du compte */}
      <Card size="3" mb="6">
        <Heading as="h2" size="5" weight="medium" mb="4">Informations du compte</Heading>
        <Flex direction="column" gap="3">
          <Flex justify="between" align="center">
            <Text size="2" weight="medium" color="gray">ID utilisateur :</Text>
            <Text size="2" style={{ fontFamily: 'monospace' }}>{user.uid}</Text>
          </Flex>
          <Flex justify="between" align="center">
            <Text size="2" weight="medium" color="gray">E-mail vérifié :</Text>
            <Badge color={user.emailVerified ? 'green' : 'orange'} variant="soft">
              {user.emailVerified ? 'Vérifié' : 'Non vérifié'}
            </Badge>
          </Flex>
          <Flex justify="between" align="center">
            <Text size="2" weight="medium" color="gray">Compte créé le :</Text>
            <Text size="2">{getAccountCreationDate()}</Text>
          </Flex>
          <Flex justify="between" align="center">
            <Text size="2" weight="medium" color="gray">Dernière connexion :</Text>
            <Text size="2">{getLastSignInDate()}</Text>
          </Flex>
        </Flex>
      </Card>

      {/* Section Zone de danger */}
      <Card size="3">
        <Heading as="h2" size="5" weight="medium" mb="4" color="red">Zone de danger</Heading>
        <Flex justify="between" align="start">
          <Flex direction="column" gap="1">
            <Text size="3" weight="medium">Supprimer le compte</Text>
            <Text size="2" color="gray">
              Cette action est irréversible. Toutes vos données seront définitivement supprimées.
            </Text>
          </Flex>
          <Button variant="outline" color="red" size="2">
            Supprimer mon compte
          </Button>
        </Flex>
      </Card>
    </Box>
  );
} 