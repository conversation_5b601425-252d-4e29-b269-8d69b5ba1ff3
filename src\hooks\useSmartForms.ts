import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SmartForm } from '@/types/dashboard';
import { ApiError, getErrorMessage } from '@/types/errors';
import { apiLogger } from '@/utils/logger';

// Clés de requête pour React Query
export const smartFormsKeys = {
  all: ['smartForms'] as const,
  lists: () => [...smartFormsKeys.all, 'list'] as const,
  list: (filters: string) => [...smartFormsKeys.lists(), { filters }] as const,
  details: () => [...smartFormsKeys.all, 'detail'] as const,
  detail: (id: string) => [...smartFormsKeys.details(), id] as const,
};

// Service API pour les SmartForms
const smartFormsApi = {
  // Récupérer tous les SmartForms
  getAll: async (): Promise<SmartForm[]> => {
    apiLogger.info('Fetching all smart forms');
    
    const response = await fetch('/api/smart-forms');
    if (!response.ok) {
      throw new ApiError(`Impossible de charger les formulaires`, response.status);
    }
    
    const data = await response.json();
    apiLogger.info('Smart forms fetched successfully', { count: data?.length || 0 });
    
    return Array.isArray(data) ? data : [];
  },

  // Récupérer un SmartForm par ID
  getById: async (id: string): Promise<SmartForm> => {
    apiLogger.info('Fetching smart form by ID', { id });
    
    const response = await fetch(`/api/smart-forms/${id}`);
    if (!response.ok) {
      if (response.status === 404) {
        throw new ApiError(`Formulaire non trouvé`, 404);
      }
      throw new ApiError(`Impossible de charger le formulaire`, response.status);
    }
    
    const data = await response.json();
    apiLogger.info('Smart form fetched successfully', { id, name: data.name });
    
    return data;
  },

  // Créer un nouveau SmartForm
  create: async (formData: Partial<SmartForm>): Promise<SmartForm> => {
    apiLogger.info('Creating new smart form', { name: formData.name });
    
    const response = await fetch('/api/smart-forms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });
    
    if (!response.ok) {
      throw new ApiError(`Impossible de créer le formulaire`, response.status);
    }
    
    const data = await response.json();
    apiLogger.info('Smart form created successfully', { id: data.id, name: data.name });
    
    return data;
  },

  // Mettre à jour un SmartForm
  update: async ({ id, ...formData }: Partial<SmartForm> & { id: string }): Promise<SmartForm> => {
    apiLogger.info('Updating smart form', { id, name: formData.name });
    
    const response = await fetch(`/api/smart-forms/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });
    
    if (!response.ok) {
      throw new ApiError(`Impossible de mettre à jour le formulaire`, response.status);
    }
    
    const data = await response.json();
    apiLogger.info('Smart form updated successfully', { id, name: data.name });
    
    return data;
  },

  // Supprimer un SmartForm
  delete: async (id: string): Promise<void> => {
    apiLogger.info('Deleting smart form', { id });
    
    const response = await fetch(`/api/smart-forms/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new ApiError(`Impossible de supprimer le formulaire`, response.status);
    }
    
    apiLogger.info('Smart form deleted successfully', { id });
  },
};

// Hook pour récupérer tous les SmartForms
export const useSmartForms = (filters?: string) => {
  return useQuery({
    queryKey: smartFormsKeys.list(filters || ''),
    queryFn: smartFormsApi.getAll,
    staleTime: 10 * 1000, // 10 secondes
    refetchInterval: 10 * 1000, // Polling toutes les 10 secondes (fallback si SSE ne marche pas)
    refetchIntervalInBackground: false, // Seulement quand l'onglet est actif
  });
};

// Hook pour récupérer un SmartForm par ID
export const useSmartForm = (id: string) => {
  return useQuery({
    queryKey: smartFormsKeys.detail(id),
    queryFn: () => smartFormsApi.getById(id),
    enabled: !!id, // Ne lance la requête que si l'ID est fourni
    staleTime: 5 * 1000, // 5 secondes
    refetchInterval: 5 * 1000, // Polling toutes les 5 secondes pour voir les nouvelles réponses
    refetchIntervalInBackground: false, // Seulement quand l'onglet est actif
  });
};

// Hook pour créer un SmartForm
export const useCreateSmartForm = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: smartFormsApi.create,
    onSuccess: (newForm) => {
      // Invalider et refetch la liste des formulaires
      queryClient.invalidateQueries({ queryKey: smartFormsKeys.lists() });
      
      // Ajouter le nouveau formulaire au cache
      queryClient.setQueryData(smartFormsKeys.detail(newForm.id), newForm);
      
      apiLogger.info('Smart form creation cache updated', { id: newForm.id });
    },
    onError: (error) => {
      apiLogger.error('Failed to create smart form', error);
    },
  });
};

// Hook pour mettre à jour un SmartForm
export const useUpdateSmartForm = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: smartFormsApi.update,
    onSuccess: (updatedForm) => {
      // Mettre à jour le cache du formulaire spécifique
      queryClient.setQueryData(smartFormsKeys.detail(updatedForm.id), updatedForm);
      
      // Invalider la liste pour refléter les changements
      queryClient.invalidateQueries({ queryKey: smartFormsKeys.lists() });
      
      apiLogger.info('Smart form update cache updated', { id: updatedForm.id });
    },
    onError: (error) => {
      apiLogger.error('Failed to update smart form', error);
    },
  });
};

// Hook pour supprimer un SmartForm
export const useDeleteSmartForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: smartFormsApi.delete,
    onSuccess: (_, deletedId) => {
      // Supprimer du cache
      queryClient.removeQueries({ queryKey: smartFormsKeys.detail(deletedId) });

      // Invalider la liste
      queryClient.invalidateQueries({ queryKey: smartFormsKeys.lists() });

      apiLogger.info('Smart form deletion cache updated', { id: deletedId });
    },
    onError: (error) => {
      apiLogger.error('Failed to delete smart form', error);
    },
  });
};

// Hook utilitaire pour forcer le refresh des SmartForms
export const useRefreshSmartForms = () => {
  const queryClient = useQueryClient();

  const refreshAll = () => {
    // Invalider toutes les listes de SmartForms
    queryClient.invalidateQueries({ queryKey: smartFormsKeys.lists() });
    apiLogger.info('Smart forms cache manually refreshed');
  };

  const refreshForm = (formId: string) => {
    // Invalider un formulaire spécifique
    queryClient.invalidateQueries({ queryKey: smartFormsKeys.detail(formId) });
    apiLogger.info('Smart form cache manually refreshed', { formId });
  };

  return { refreshAll, refreshForm };
};
