'use client';
import React, { useState } from 'react';
import { Box, Flex, Card, Heading, Text, Button, Avatar, Grid, Separator, TextField, IconButton, Switch, AlertDialog, Link, Badge } from '@radix-ui/themes';
import { MagnifyingGlassIcon, PlusIcon, GearIcon, TrashIcon, CodeIcon, MagicWandIcon } from '@radix-ui/react-icons';
import { formatDateFR } from '@/utils/dateUtils';

// Données mock avec logoSrc utilisant Simple Icons CDN
const initialConnectedSources = [
  {
    id: 'quickbooks',
    name: 'QuickBooks Online',
    type: 'Facturation',
    status: 'Connecté',
    lastSync: formatDateFR(new Date()), // Date actuelle
    logoSrc: 'https://cdn.simpleicons.org/quickbooks/0076C5', // Couleur QuickBooks
    totalFieldsAvailable: 120,
    mappedFieldsCount: 95,
    completionRate: 0.85, // 85%
  },
  {
    id: 'odoo',
    name: 'Odoo',
    type: 'ERP/CRM',
    status: 'Connecté',
    lastSync: formatDateFR(new Date()), // Date actuelle
    logoSrc: 'https://cdn.simpleicons.org/odoo/714B67', // Provenant du catalogue
    totalFieldsAvailable: 250,
    mappedFieldsCount: 200,
    completionRate: 0.70, // 70%
  },
  // Ajoute d'autres sources connectées ici si nécessaire
];

const unsortedCatalog = [
  { id: 'salesforce', name: 'Salesforce', type: 'CRM', desc: 'Synchronisez prospects, contacts et données d\'entreprise.', connected: false, logoSrc: 'https://cdn.simpleicons.org/salesforce/00A1E0' },
  { id: 'hubspot', name: 'HubSpot CRM', type: 'CRM', desc: 'Intégrez avec vos contacts et transactions HubSpot.', connected: false, logoSrc: 'https://cdn.simpleicons.org/hubspot/FF7A59' },
  { id: 'zoho', name: 'Zoho CRM', type: 'CRM', desc: 'Connectez-vous à Zoho CRM pour les données clients.', connected: false, logoSrc: 'https://cdn.simpleicons.org/zoho/F8481C' },
  { id: 'odoo', name: 'Odoo', type: 'ERP/CRM', desc: 'Tirez parti des capacités intégrées d\'Odoo.', connected: true, logoSrc: 'https://cdn.simpleicons.org/odoo/714B67' },
  { id: 'sheets', name: 'Google Sheets', type: 'Tableur', desc: 'Lisez et écrivez des données depuis Google Sheets.', connected: false, logoSrc: 'https://cdn.simpleicons.org/googlesheets/0F9D58' },
  { id: 'airtable', name: 'Airtable', type: 'Base de données', desc: 'Connectez-vous à vos bases et tables Airtable.', connected: false, logoSrc: 'https://cdn.simpleicons.org/airtable/18BFFF' },
  { id: 'fbads', name: 'Facebook Ads', type: 'Publicité', desc: 'Gérez les campagnes et comptes Facebook Ads.', connected: false, logoSrc: 'https://cdn.simpleicons.org/facebook/1877F2' },
  { id: 'googleads', name: 'Google Ads', type: 'Publicité', desc: 'Gérez les campagnes et comptes Google Ads.', connected: false, logoSrc: 'https://cdn.simpleicons.org/googleads/4285F4' },
  { id: 'stripe', name: 'Stripe', type: 'Paiement', desc: 'Connectez vos données de paiement Stripe.', connected: false, logoSrc: 'https://cdn.simpleicons.org/stripe/6772E5' }, 
  { id: 'mailchimp', name: 'Mailchimp', type: 'Emailing', desc: 'Synchronisez vos listes et campagnes Mailchimp.', connected: false, logoSrc: 'https://cdn.simpleicons.org/mailchimp/FFE01B' },
  { 
    id: 'quickbooks',
    name: 'QuickBooks Online',
    type: 'Facturation',
    desc: 'Gérez votre facturation et comptabilité avec QuickBooks Online.', 
    connected: true, 
    logoSrc: 'https://cdn.simpleicons.org/quickbooks/0076C5' 
  },
  { 
    id: 'notion', 
    name: 'Notion', 
    type: 'Productivité/Base de données', 
    desc: 'Connectez vos pages, bases de données et contenus Notion.', 
    connected: false, 
    logoSrc: 'https://cdn.simpleicons.org/notion/000000' 
  },
  { 
    id: 'attio', 
    name: 'Attio', 
    type: 'CRM', 
    desc: 'Connectez votre CRM Attio pour synchroniser contacts, entreprises et listes.', 
    connected: false, 
    logoSrc: 'https://cdn.simpleicons.org/attio/7F5FFD' // Couleur Attio (violet)
  },
];

const catalog = unsortedCatalog.sort((a, b) => a.name.localeCompare(b.name));

export default function DataSourcesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [appsCatalog, setAppsCatalog] = useState(catalog);
  const [connectedSources, setConnectedSources] = useState(initialConnectedSources);
  const [isConfirmDisconnectModalOpen, setConfirmDisconnectModalOpen] = useState(false);
  const [appPendingDisconnect, setAppPendingDisconnect] = useState<string | null>(null);

  const filteredCatalog = appsCatalog.filter(app => 
    !app.connected &&
    (app.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    app.type.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleConnectToggle = (appId: string, checked: boolean) => {
    setAppsCatalog(prevCatalog =>
      prevCatalog.map(app =>
        app.id === appId ? { ...app, connected: checked } : app
      )
    );
    if (checked) {
      const appToConnect = appsCatalog.find(app => app.id === appId);
      if (appToConnect) {
        setConnectedSources(prevSources => [...prevSources, {
          id: appToConnect.id,
          name: appToConnect.name,
          type: appToConnect.type,
          status: 'Connecté',
          lastSync: formatDateFR(new Date()),
          logoSrc: appToConnect.logoSrc,
          totalFieldsAvailable: 0,
          mappedFieldsCount: 0,
          completionRate: 0,
        }]);
      }
    } else {
      setConnectedSources(prevSources => prevSources.filter(src => src.id !== appId));
    }
  };

  const handleAttemptDisconnect = (appId: string) => {
    setAppPendingDisconnect(appId);
    setConfirmDisconnectModalOpen(true);
  };

  const confirmDisconnect = () => {
    if (appPendingDisconnect) {
      handleConnectToggle(appPendingDisconnect, false);
    }
    setConfirmDisconnectModalOpen(false);
    setAppPendingDisconnect(null);
  };

  const cancelDisconnect = () => {
    setConfirmDisconnectModalOpen(false);
    setAppPendingDisconnect(null);
    // Le Switch devrait revenir à son état précédent car son 'checked' est lié à l'état global.
  };

  return (
    <Box style={{ maxWidth: 'var(--layout-max-width, 1280px)', margin: '0 auto' }} p={{initial: '3', md: '6'}}>
      {/* Header de la page */}
      <Flex direction={{ initial: 'column', md: 'row' }} justify="between" align={{initial: 'start', md: 'center'}} mb="6" gap="4">
        <Box>
          <Heading as="h1" size="8" weight="bold" mb="1">
            Sources de Données
          </Heading>
          <Text color="gray" size="4">Connectez et gérez vos applications et services externes.</Text>
        </Box>
      </Flex>

      {/* Section des sources connectées */}
      <Box mb="8">
        <Heading as="h2" size="6" weight="medium" mb="4" color="gray">Vos Applications Connectées</Heading>
        {connectedSources.length === 0 ? (
          <Card variant="surface" style={{borderStyle: 'dashed'}}>
            <Flex direction="column" align="center" justify="center" gap="3" style={{minHeight: 180, padding: 'var(--space-5)'}}>
                <Avatar fallback="🔌" color="gray" highContrast size="5" radius="full" />
                <Heading as="h3" size="4" weight="medium" color="gray">Aucune application connectée</Heading>
                <Text size="2" color="gray" align="center">Connectez votre première application depuis le catalogue ci-dessous.</Text>
            </Flex>
          </Card>
        ) : (
          <Grid columns={{ initial: '1', sm: '2', md: '3' }} gap="5">
          {connectedSources.map(src => (
              <Card key={src.id} variant="surface" size="2" style={{display: 'flex', flexDirection: 'column'}}> 
                <Flex align="start" gap="3" mb="3">
                  <Avatar src={src.logoSrc} fallback={src.name[0]} size="3" radius="medium" 
                          style={{border: '1px solid var(--gray-a5)', padding: 'var(--space-1)'}}/>
                  <Box style={{flexGrow: 1}}>
                      <Heading as="h3" size="3" weight="medium" trim="start">{src.name}</Heading>
                      <Text size="1" color="gray">{src.type}</Text>
                  </Box>
                </Flex>
                
                <Box style={{flexGrow: 1}} mb="3">
                    <Text as="div" size="1" color="gray" mb="1">
                        Champs accessibles : <Text weight="bold">{src.totalFieldsAvailable}</Text>
                    </Text>
                    <Text as="div" size="1" color="gray" mb="1">
                        Champs mappés : <Text weight="bold">{src.mappedFieldsCount}</Text>
                    </Text>
                    <Flex align="center" gap="2" mb="1">
                        <Text as="div" size="1" color="gray">
                            Taux de complétion : <Text weight="bold">{(src.completionRate * 100).toFixed(0)}%</Text>
                        </Text>
                        <Button 
                            variant="soft" 
                            color="blue" 
                            size="1" 
                            onClick={() => console.log(`Enrichir les données pour ${src.name}`)}
                            style={{cursor: 'pointer'}}
                        >
                            <MagicWandIcon width={12} height={12} style={{marginRight: 'var(--space-1)'}}/>
                            Enrichir
                        </Button>
                    </Flex>
                    {/* On pourrait ajouter une barre de progression simple ici si désiré */}
                </Box>

                <Flex align="center" justify="between" style={{ marginTop: 'auto', paddingTop: 'var(--space-2)', borderTop: '1px solid var(--gray-a5)' }}> 
                    <Flex asChild align="center" gap="2">
                      <label 
                        htmlFor={`switch-connected-${src.id}`} 
                        style={{ cursor: 'pointer', display: 'inline-flex', alignItems: 'center' }}
                      >
                        <Switch 
                            id={`switch-connected-${src.id}`}
                            checked={true} // Toujours coché car dans la section "connecté"
                            onCheckedChange={(checked) => {
                              if (!checked) { // Gérer uniquement la déconnexion
                                handleAttemptDisconnect(src.id);
                              }
                            }}
                            color="green"
                            size="1"
                            title="Connecté - Cliquer pour déconnecter"
                        />
                        <Text size="2" color="green" weight="medium" style={{ marginLeft: 'var(--space-2)' }}>Connecté</Text>
                      </label>
                    </Flex>
                    <IconButton 
                        variant="ghost" 
                        color="gray" 
                        size="2" 
                        onClick={() => console.log(`Ouvrir paramètres pour ${src.name}`)}
                        title="Paramètres"
                        style={{cursor: "pointer"}}
                    >
                        <GearIcon width={16} height={16}/>
                    </IconButton>
                </Flex>
              </Card>
            ))}
          </Grid>
        )}
      </Box>
      
      <Separator my={{initial: '6', md: '7'}} size="4" />

      {/* Catalogue des sources disponibles */}
      <Box>
        <Flex direction={{ initial: 'column', sm: 'row' }} justify="between" align={{ initial: 'stretch', sm: 'center' }} mb="5" gap="4">
            <Heading as="h2" size="6" weight="medium" color="gray">Catalogue d'Applications</Heading>
            <TextField.Root 
                placeholder="Rechercher une application..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="2"
                style={{ minWidth: '250px', maxWidth: '400px'}}
            >
                <TextField.Slot>
                    <MagnifyingGlassIcon height="16" width="16" />
                </TextField.Slot>
            </TextField.Root>
        </Flex>

        <Grid columns={{ initial: '1', sm: '2', md: '3', lg: '4' }} gap="5">
            {filteredCatalog.map(app => (
            <Card key={app.id} variant="surface" size="2" style={{display: 'flex', flexDirection: 'column'}}>
                <Flex align="start" gap="3" mb="2">
                    <Avatar src={app.logoSrc} fallback={app.name[0]} size="3" radius="medium"
                            style={{border: '1px solid var(--gray-a5)', padding: 'var(--space-1)'}}/>
                    <Box style={{flexGrow: 1}}>
                        <Heading as="h3" size="3" weight="medium" trim="start">{app.name}</Heading>
                        <Text size="1" color="gray">{app.type}</Text>
                    </Box>
                </Flex>
                <Text size="2" color="gray" mb="auto" style={{flexGrow: 1, minHeight: '60px'}}>{app.desc}</Text> {/* mb="auto" pour pousser bouton en bas */} 
                <Flex 
                    asChild 
                    align="center" 
                    gap="2" 
                    style={{ marginTop: 'var(--space-3)', width: '100%' }}
                >
                  <label 
                    htmlFor={`switch-catalog-${app.id}`} 
                    style={{ cursor: 'pointer', display: 'inline-flex', alignItems: 'center', width: '100%' }}
                  >
                    <Switch 
                        id={`switch-catalog-${app.id}`} 
                        checked={app.connected} 
                        onCheckedChange={(checked) => {
                          if (checked) {
                            handleConnectToggle(app.id, true);
                          } else {
                            handleAttemptDisconnect(app.id);
                          }
                        }}
                        color={app.connected ? "green" : "gray"}
                        size="1" // Garder la taille cohérente
                    />
                    <Text size="2" color={app.connected ? "green" : "gray"} style={{ marginLeft: 'var(--space-2)' }}>
                        {app.connected ? "Connecté" : "Déconnecté"}
                    </Text>
                  </label>
                </Flex>
            </Card>
            ))}
            
            {/* Carte pour ajout custom - maintenant à l'intérieur de la Grid */}
            { (!searchTerm || (searchTerm && filteredCatalog.length === 0)) && (
                <Card variant="surface" size="2" style={{
                    border: '1px dashed var(--gray-a7)', 
                    display: 'flex', 
                    flexDirection: 'column',
                    // Le marginTop n'est plus nécessaire ici car la grille gère l'espacement via `gap`
                }}>
                    <Flex align="start" gap="3" mb="2">
                        <Avatar 
                            fallback={<CodeIcon width={22} height={22} color='var(--gray-11)'/>}
                            size="3" 
                            radius="medium"
                            color="gray"
                            style={{border: '1px solid var(--gray-a5)', padding: 'var(--space-1)', backgroundColor: 'var(--gray-a3)'}}
                        />
                        <Box style={{flexGrow: 1}}>
                            <Heading as="h3" size="3" weight="medium" trim="start">Application Personnalisée</Heading>
                            <Text size="1" color="gray">Personnalisé</Text>
                        </Box>
                    </Flex>
                    <Text size="2" color="gray" mb="auto" style={{flexGrow: 1, minHeight: '60px'}}>
                        Intégrez une autre API ou un service via webhook pour des besoins spécifiques.
                    </Text>
                    <Flex align="center" justify="end" style={{ marginTop: 'var(--space-3)', width: '100%' }}> 
                        <Button variant="outline" color="gray" size="2" style={{ cursor: 'pointer' }}>
                            <PlusIcon width={14} height={14} style={{marginRight: 'var(--space-1)'}}/> Ajouter
                        </Button>
                    </Flex>
                </Card>
            )}
        </Grid>
        {filteredCatalog.length === 0 && searchTerm && (
             <Flex justify="center" align="center" style={{minHeight: 150, gridColumn: 'span / span all' /* Pour s'assurer qu'il prend toute la largeur si la grille est vide */}}>
                <Text color="gray">Aucune application ne correspond à "{searchTerm}".</Text>
            </Flex>
        )}
      </Box>

      <AlertDialog.Root open={isConfirmDisconnectModalOpen} onOpenChange={setConfirmDisconnectModalOpen}>
        <AlertDialog.Content style={{ maxWidth: 450 }}>
          <AlertDialog.Title>Confirmer la déconnexion</AlertDialog.Title>
          <AlertDialog.Description size="2">
            Si vous déconnectez cette application, les données ne seront plus accessibles pour les SmartForms. 
            Si des SmartForms sont en cours, l'utilisateur final ne pourra plus remplir les champs concernés.
            Êtes-vous sûr de vouloir continuer ?
          </AlertDialog.Description>
          <Flex gap="3" mt="4" justify="end">
            <AlertDialog.Cancel>
              <Button variant="soft" color="gray" onClick={cancelDisconnect}>
                Annuler
              </Button>
            </AlertDialog.Cancel>
            <AlertDialog.Action>
              <Button variant="solid" color="red" onClick={confirmDisconnect}>
                Déconnecter
              </Button>
            </AlertDialog.Action>
          </Flex>
        </AlertDialog.Content>
      </AlertDialog.Root>

      <Flex justify="center" mt={{initial: '7', md: '8'}} py="5">
        <Text size="2" color="gray">
          Votre application n'est pas listée ? 
          <Link href="/dashboard/become-partner" style={{textDecoration: 'underline', marginLeft: 'var(--space-1)'}}>
             Devenez partenaire Diktasis
          </Link>
          .
        </Text>
      </Flex>
    </Box>
  );
} 