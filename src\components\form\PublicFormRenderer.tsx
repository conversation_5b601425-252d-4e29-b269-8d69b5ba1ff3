'use client';

import React from 'react';
import { 
    <PERSON><PERSON>, 
    Card, 
    Flex, 
    <PERSON>ing, 
    Text, 
    TextField, 
    TextArea, 
    Checkbox, 
    Select, 
    Spinner,
    Callout
} from '@radix-ui/themes';
import { FormConfig, FormField, FieldOption } from '@/types/form'; // Importer les types partagés

interface PublicFormRendererProps {
  formConfig: FormConfig;
  formData: { [key: string]: any };
  onInputChange: (fieldId: string, value: any) => void;
  onSubmit: (event: React.FormEvent) => Promise<void>;
  isSubmitting: boolean;
  submissionError: string | null;
  // submissionSuccess n'est pas géré ici, le parent gère l'affichage du message de succès
  // Le statut 'preview' est géré par la prop readOnly sur les champs
}

const PublicFormRenderer: React.FC<PublicFormRendererProps> = ({
  formConfig,
  formData,
  onInputChange,
  onSubmit,
  isSubmitting,
  submissionError,
}) => {

  const isPreview = formConfig.status === 'preview';

  return (
    <form onSubmit={onSubmit}>
      {formConfig.instructions && (
        <Callout.Root color="blue" mb="5" variant='soft'>
          <Callout.Icon>
            <InfoCircledIcon />
          </Callout.Icon>
          <Callout.Text style={{ whiteSpace: 'pre-wrap'}}>
            {formConfig.instructions}
          </Callout.Text>
        </Callout.Root>
      )}
      <Flex direction="column" gap="4">
        {formConfig.fields.map((field: FormField) => (
          <Flex key={field.id} direction="column" gap="1">
            <label htmlFor={field.id} style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
              <Text size="3" weight="medium" style={{ marginRight: 'var(--space-2)' }}>
                {field.label || field.name}
                {field.required && !isPreview && <Text color="tomato"> *</Text>}
              </Text>
              {field.example && (
                <Text size="1" color="gray" style={{ fontStyle: 'italic' }}>
                  (ex: {field.example})
                </Text>
              )}
            </label>
            {field.description && (
              <Text size="1" color="gray" mb="1" style={{ fontStyle: 'italic' }}>
                {field.description}
              </Text>
            )}
            {field.type === 'textarea' ? (
              <TextArea
                id={field.id}
                value={formData[field.id] || ''}
                onChange={(e) => onInputChange(field.id, e.target.value)}
                placeholder={field.placeholder}
                required={field.required && !isPreview}
                rows={field.rows || 4}
                disabled={isSubmitting || isPreview || field.readOnly}
                style={{backgroundColor: (isPreview || field.readOnly) ? 'var(--gray-a3)': undefined}}
              />
            ) : field.type === 'select' ? (
              <Select.Root
                name={field.id}
                value={formData[field.id] || ''}
                onValueChange={(value) => onInputChange(field.id, value)}
                required={field.required && !isPreview}
                disabled={isSubmitting || isPreview || field.readOnly}
              >
                <Select.Trigger 
                  placeholder={field.placeholder || 'Sélectionnez...'} 
                  style={{backgroundColor: (isPreview || field.readOnly) ? 'var(--gray-a3)': undefined, width: '100%'}}
                />
                <Select.Content>
                  {field.options?.map((option) => {
                    const opt = typeof option === 'string' ? { value: option, label: option } : option;
                    return (
                      <Select.Item key={opt.value} value={opt.value}>
                        {opt.label}
                      </Select.Item>
                    );
                  })}
                </Select.Content>
              </Select.Root>
            ) : field.type === 'checkbox' ? (
              <Text as="label" size="2" style={{display: 'flex', alignItems: 'center', gap: 'var(--space-2)'}}>
                <Checkbox
                  id={field.id}
                  checked={formData[field.id] || false}
                  onCheckedChange={(checked) => onInputChange(field.id, checked)}
                  required={field.required && !isPreview}
                  disabled={isSubmitting || isPreview || field.readOnly}
                  style={{backgroundColor: (isPreview || field.readOnly) ? 'var(--gray-a3)': undefined}}
                /> 
                {field.label || field.name} 
              </Text>
            ) : (
              <TextField.Root
                id={field.id}
                type={field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : 'text'} // date non géré par TextField.Root
                value={formData[field.id] || ''}
                onChange={(e) => onInputChange(field.id, e.target.value)}
                placeholder={field.placeholder}
                required={field.required && !isPreview}
                disabled={isSubmitting || isPreview || field.readOnly}
                min={field.type === 'number' ? field.min : undefined}
                max={field.type === 'number' ? field.max : undefined}
                style={{backgroundColor: (isPreview || field.readOnly) ? 'var(--gray-a3)': undefined}}
              />
            )}
          </Flex>
        ))}
        {!isPreview && (
            <Button type="submit" disabled={isSubmitting} size="3" mt="3">
            {isSubmitting ? <Spinner /> : (formConfig.submitButtonText || 'Soumettre')}
            </Button>
        )}
        {submissionError && !isPreview && (
          <Callout.Root color="red" mt="3">
            <Callout.Text>{submissionError}</Callout.Text>
          </Callout.Root>
        )}
      </Flex>
    </form>
  );
};

export default PublicFormRenderer;

// Ajouter InfoCircledIcon aux imports si nécessaire pour Callout
import { InfoCircledIcon } from '@radix-ui/react-icons'; 