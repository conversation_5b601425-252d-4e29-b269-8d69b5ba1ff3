'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { StatsOverview } from '../../components/dashboard/StatsOverview';
import { OnboardingGuide } from '../../components/dashboard/OnboardingGuide';
import { DemoDataExplainer } from '../../components/dashboard/DemoDataExplainer';
import { ConnectedSourcesOverview } from '../../components/dashboard/ConnectedSourcesOverview';
import { AdvancedStatsButton } from '../../components/dashboard/AdvancedStatsButton';
import { mockSmartForms } from '../../mock/data';
import { useRouter } from 'next/navigation';
import { Box, Button, Card, Flex, Grid, Table, Text, Badge, Heading, Avatar, Tooltip, DropdownMenu, IconButton } from '@radix-ui/themes';
import { PlusCircledIcon, ClockIcon, CheckCircledIcon, ExclamationTriangleIcon, DotsHorizontalIcon, InfoCircledIcon, EyeOpenIcon, CopyIcon, Share1Icon, TrashIcon } from '@radix-ui/react-icons';
import { formatDateFR } from '@/utils/dateUtils';
import { FormStatus, SmartForm } from '@/types/dashboard';

// Composant mock d'activité récente
const mockActivities = [
  {
    id: 1,
    user: 'Alice',
    action: "a mis à jour le profil d'Innovatech Ltd.",
    target: 'Innovatech Ltd.',
    time: 'il y a 2 heures',
  },
  {
    id: 2,
    user: 'Bob',
    action: 'a complété le profil via le lien.',
    target: 'Synergy Solutions',
    time: 'il y a 5 heures',
  },
  {
    id: 3,
    user: 'Alice',
    action: 'a envoyé le lien de complétion à QuantumLeap Inc.',
    target: 'QuantumLeap Inc.',
    time: 'il y a 1 jour',
  },
  {
    id: 4,
    user: 'System',
    action: 'a suggéré du contenu pour EcoPower Corp.',
    target: 'EcoPower Corp.',
    time: 'il y a 2 jours',
  },
];

function RecentActivity() {
  return (
    <Card size="3">
      <Flex justify="between" align="center" mb="4">
        <Heading as="h2" size="5" weight="bold">Activité récente</Heading>
        <Tooltip content="Historique des dernières actions effectuées sur la plateforme">
          <InfoCircledIcon width={14} height={14} color="var(--gray-9)" style={{ cursor: 'help' }} />
        </Tooltip>
      </Flex>
      <Flex direction="column" gap="4">
        {mockActivities.map((act) => (
          <Flex key={act.id} gap="3" align="start">
            <Avatar 
              size="2"
              fallback={act.user[0]} 
              color={act.user === 'System' ? "gray" : "blue"}
              highContrast
            />
            <Box style={{ flexGrow: 1 }}>
              <Text size="2" color="gray">
                <Text weight="bold" color="gray">{act.user}</Text> {act.action} <Text weight="medium" color="gray">{act.target}</Text>
              </Text>
              <Text size="1" color="gray" style={{ opacity: 0.8 }}>{act.time}</Text>
            </Box>
          </Flex>
        ))}
      </Flex>
    </Card>
  );
}

export default function DashboardPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [recentForms, setRecentForms] = useState(mockSmartForms);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isClient) {
      const updatedForms = mockSmartForms.map(form => {
        try {
          // Supposons que l'ID public du formulaire est stocké ou peut être dérivé de form.id
          // Pour la démo, si le form.id est celui utilisé pour `formConfig-${formId}`
          const storedConfigData = localStorage.getItem(`formConfig-${form.id}`);
          if (storedConfigData) {
            const config = JSON.parse(storedConfigData);
            if (config.status === 'completed') {
              return { ...form, status: 'completed' as FormStatus }; // Assurez-vous que FormStatus inclut 'completed'
            }
          }
        } catch (e) {
          console.error("Erreur lors de la lecture du statut du formulaire depuis localStorage:", e);
        }
        return form;
      });
      setRecentForms(updatedForms);
    }
  }, [isClient]);

  // Version de débogage - on force l'affichage même en cas de problème d'auth
  console.log('Dashboard: loading =', loading, 'user =', user?.uid || 'no user');
  
  if (loading) {
    return (
      <Box p="6">
        <Text>Chargement en cours...</Text>
        <Text size="1" color="gray">Si cela prend trop de temps, il y a peut-être un problème d'authentification.</Text>
      </Box>
    );
  }

  const firstName = user?.displayName?.split(' ')[0] || 'Utilisateur';

  return (
    <Grid columns={{ initial: "1", lg: "4" }} gap="6">
      {/* Colonne principale (3/4) */}
      <Box style={{ gridColumn: "span 3" }}>
        {/* Header avec message d'accueil */}
        <Flex direction={{ initial: "column", md: "row" }} justify="between" align={{ md: "center" }} gap="4" mb="6">
          <Box>
            <Heading as="h1" size="8" weight="bold" mb="1">Bienvenue, {firstName} !</Heading>
            <Text color="gray" size="4">Voici un aperçu de vos activités.</Text>
          </Box>
          <Button 
            size="3"
            variant="solid"
            color="blue"
            highContrast
            onClick={() => router.push('/dashboard/new-smart-forms')}
            style={{ gap: 'var(--space-2)' }}
          >
            <PlusCircledIcon width={20} height={20} />
            Nouveau SmartForm
          </Button>
        </Flex>

        {/* Section onboarding pour nouveaux utilisateurs */}
        <Box mb="5">
          <OnboardingGuide />
        </Box>

        {/* Indicateur données démo */}
        <Box mb="5">
          <DemoDataExplainer />
        </Box>

        {/* KPIs opérationnels */}
        <Box mb="6">
          <Flex justify="between" align="center" mb="4">
            <Heading as="h2" size="6" weight="bold">Indicateurs Clés</Heading>
            <Tooltip content="Métriques importantes pour suivre l'activité de vos SmartForms">
              <InfoCircledIcon width={14} height={14} color="var(--gray-9)" style={{ cursor: 'help' }} />
            </Tooltip>
          </Flex>
          <StatsOverview />
        </Box>

        {/* Tableau de suivi des SmartForms enrichi */}
        <Card size="3">
          <Flex justify="between" align="center" mb="4">
            <Heading as="h2" size="6" weight="bold">Suivi des SmartForms</Heading>
            <Tooltip content="Liste de tous vos SmartForms avec leur statut et progression">
              <InfoCircledIcon width={14} height={14} color="var(--gray-9)" style={{ cursor: 'help' }} />
            </Tooltip>
          </Flex>
          <Table.Root variant="surface" size="2">
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeaderCell>Nom du Formulaire</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Statut</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Progression</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Créé le</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell align="right">Actions</Table.ColumnHeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {recentForms.map((form) => (
                <Table.Row key={form.id} align="center">
                  <Table.Cell>
                    <Box>
                      <Text weight="medium" color="gray" size="2" as="div">{form.name}</Text>
                      {form.description && (
                        <Text size="1" color="gray" style={{ opacity: 0.8 }} as="div">
                          {form.description}
                        </Text>
                      )}
                    </Box>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge 
                      size="1"
                      variant="soft"
                      color={
                        form.status === 'draft' ? 'yellow' :
                        form.status === 'sent' ? 'blue' :
                        form.status === 'completed' ? 'green' :
                        'green'
                      }
                    >
                      {form.status === 'draft' ? (
                        <ExclamationTriangleIcon width={14} height={14} style={{ marginRight: 'var(--space-1)' }} />
                      ) : form.status === 'sent' ? (
                        <ClockIcon width={14} height={14} style={{ marginRight: 'var(--space-1)' }} />
                      ) : (
                        <CheckCircledIcon width={14} height={14} style={{ marginRight: 'var(--space-1)' }} />
                      )}
                      {form.status === 'draft' ? 'Brouillon' :
                       form.status === 'sent' ? 'Envoyé' :
                       form.status === 'completed' ? 'Complété' :
                       'Complété'}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <Box style={{ width: 100, background: 'var(--gray-a3)', borderRadius: 'var(--radius-3)', height: 8 }}>
                      <Box 
                        style={{ 
                          height: 8, 
                          borderRadius: 'var(--radius-3)', 
                          background: 
                            form.status === 'draft' ? 'var(--yellow-9)' :
                            form.status === 'sent' ? 'var(--blue-9)' :
                            form.status === 'completed' ? 'var(--green-9)' :
                            'var(--green-9)', 
                          width: form.status === 'draft' ? '30%' : form.status === 'sent' ? '60%' : form.status === 'completed' ? '100%' : '100%'
                        }}
                      />
                    </Box>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray" size="2">{formatDateFR(new Date(form.createdAt))}</Text>
                  </Table.Cell>
                  <Table.Cell align="right">
                    <DropdownMenu.Root>
                      <DropdownMenu.Trigger>
                        <IconButton variant="ghost" color="gray" size="2">
                          <DotsHorizontalIcon width={16} height={16}/>
                        </IconButton>
                      </DropdownMenu.Trigger>
                      <DropdownMenu.Content align="end" size="2">
                        <DropdownMenu.Item onClick={() => router.push(`/dashboard/smart-forms/${form.id}`)}>
                          <EyeOpenIcon width={14} height={14} style={{ marginRight: 'var(--space-2)' }}/>
                          Voir les détails
                        </DropdownMenu.Item>
                        <DropdownMenu.Item onClick={() => console.log('Partager form:', form.id)}>
                          <Share1Icon width={14} height={14} style={{ marginRight: 'var(--space-2)' }}/>
                          Partager
                        </DropdownMenu.Item>
                        <DropdownMenu.Item onClick={() => console.log('Dupliquer form:', form.id)}>
                          <CopyIcon width={14} height={14} style={{ marginRight: 'var(--space-2)' }}/>
                          Dupliquer
                        </DropdownMenu.Item>
                        <DropdownMenu.Separator />
                        <DropdownMenu.Item color="red" onClick={() => console.log('Archiver form:', form.id)}>
                          <TrashIcon width={14} height={14} style={{ marginRight: 'var(--space-2)' }}/>
                          Archiver
                        </DropdownMenu.Item>
                      </DropdownMenu.Content>
                    </DropdownMenu.Root>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Card>
      </Box>

      {/* Sidebar (1/4) */}
      <Box style={{ gridColumn: "span 1" }}>
        <Flex direction="column" gap="5">
          {/* Sources connectées */}
          <ConnectedSourcesOverview />
          
          {/* Activité récente */}
          <RecentActivity />
          
          {/* Bouton stats avancées */}
          <AdvancedStatsButton />
        </Flex>
      </Box>
    </Grid>
  );
} 