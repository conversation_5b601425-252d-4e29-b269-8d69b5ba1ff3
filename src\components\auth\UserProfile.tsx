"use client";
import { Button, Flex, Text, Avatar, DropdownMenu } from "@radix-ui/themes";
import { ExitIcon, PersonIcon, GearIcon } from "@radix-ui/react-icons";
import { User } from "firebase/auth";
import { logout } from "../../firebaseConfig";
import { authLogger } from "@/utils/logger";

interface UserProfileProps {
  user: User;
  onError: (error: string) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ user, onError }) => {
  const handleLogout = async () => {
    try {
      authLogger.info('User logging out', { userId: user.uid });
      await logout();
      authLogger.info('User logged out successfully');
    } catch (error) {
      authLogger.error('Logout failed', error);
      onError('Erreur lors de la déconnexion');
    }
  };

  const displayName = user.displayName || user.email || 'Utilisateur';
  const initials = displayName
    .split(' ')
    .map(name => name[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Button variant="ghost" style={{ padding: '8px' }}>
          <Flex align="center" gap="2">
            <Avatar
              size="2"
              src={user.photoURL || undefined}
              fallback={initials}
            />
            <Text size="2" weight="medium">
              {displayName}
            </Text>
          </Flex>
        </Button>
      </DropdownMenu.Trigger>
      
      <DropdownMenu.Content>
        <DropdownMenu.Item>
          <PersonIcon width="16" height="16" />
          Profil
        </DropdownMenu.Item>
        
        <DropdownMenu.Item>
          <GearIcon width="16" height="16" />
          Paramètres
        </DropdownMenu.Item>
        
        <DropdownMenu.Separator />
        
        <DropdownMenu.Item color="red" onClick={handleLogout}>
          <ExitIcon width="16" height="16" />
          Se déconnecter
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
