import { NextResponse } from 'next/server';
import { mockFormConfigs } from './mockConfigStore'; // Garder pour la config si encore utilisé
import { mockSmartForms } from '@/mock/data'; // Importer le mockSmartForms mutable
import { SmartFormResponse } from '@/types/dashboard'; // Importer le type SmartFormResponse
import { broadcastEvent } from '@/app/api/events/route';

// TODO: Définir un type plus précis pour la configuration du formulaire si nécessaire
interface FormConfig {
  formId: string;
  title: string;
  description: string;
  fields: any[]; // Simplifié pour l'instant, à aligner avec PublicSmartFormPage
}

// Base de données fictive des configurations de formulaires
const mockFormConfigs: { [key: string]: FormConfig } = {
  'form-demo-1': {
    formId: 'form-demo-1',
    title: 'Formulaire de Démonstration N°1',
    description: 'Ceci est le premier formulaire de démonstration. Merci de le remplir attentivement.',
    fields: [
      { id: 'nom_prenom', name: 'nom_prenom', label: 'Nom et Prénom', type: 'text', required: true, placeholder: 'ex: <PERSON> Dupont', example: 'Votre nom complet' },
      { id: 'email_contact', name: 'email_contact', label: 'Votre Email', type: 'email', required: true, placeholder: 'ex: <EMAIL>', example: 'Adresse email valide' },
      {
        id: 'siret_number',
        name: 'Numéro SIRET',
        label: 'Numéro SIRET',
        type: 'text',
        required: false,
        placeholder: '123 456 789 00012',
        example: '14 chiffres'
      },
      { id: 'satisfaction', name: 'satisfaction', label: 'Êtes-vous satisfait du service ?', type: 'select', options: ['Oui', 'Non', 'Peut-être'], required: true, placeholder: 'Choisissez une option' },
    ],
  },
  'form-demo-2': {
    formId: 'form-demo-2',
    title: 'Formulaire de Feedback Projet',
    description: 'Donnez-nous votre avis sur le projet Alpha.',
    fields: [
      { id: 'nom_projet', name: 'nom_projet', label: 'Nom du Projet', type: 'text', required: true, value: 'Projet Alpha', readOnly: true },
      { id: 'commentaire_general', name: 'commentaire_general', label: 'Commentaire général', type: 'textarea', required: true, rows: 5 },
      { id: 'note_globale', name: 'note_globale', label: 'Note globale (sur 5)', type: 'number', required: true, min: 1, max: 5 },
      { id: 'recommander', name: 'recommander', label: 'Recommanderiez-vous ce projet ?', type: 'checkbox', required: false },
    ],
  },
  // Ajoutez d'autres configurations de formulaires ici pour vos démos
  // par exemple, un formulaire avec les champs de la page [formId]/page.tsx initiale
  'default-form': {
    formId: 'default-form',
    title: 'Formulaire de Contact Standard',
    description: 'Merci de remplir les informations demandées ci-dessous.',
    fields: [
        { id: 'nom_complet', name: 'nom_complet', label: 'Nom complet', type: 'text', required: true },
        { id: 'email', name: 'email', label: 'Adresse e-mail', type: 'email', required: true },
        { id: 'entreprise', name: 'entreprise', label: 'Nom de l\'entreprise', type: 'text', required: false },
        { id: 'poste', name: 'poste', label: 'Poste occupé', type: 'text', required: false },
        { id: 'feedback', name: 'feedback', label: 'Votre feedback', type: 'textarea', required: true },
        { 
            id: 'service_interesse', 
            name: 'service_interesse', 
            label: 'Quel service vous intéresse ?', 
            type: 'select', 
            required: true,
            options: ['Service A', 'Service B', 'Service C']
        },
        { id: 'consent_rgpd', name: 'consent_rgpd', label: 'J\'accepte les termes et conditions', type: 'checkbox', required: true },
    ],
  }
};

export async function GET(
  request: Request,
  { params }: { params: { formId: string } }
) {
  const formId = params.formId;

  // Priorité à mockSmartForms pour la configuration si l'ID correspond à un formulaire du dashboard
  const dashboardFormConfig = mockSmartForms.find(f => f.id === formId);
  if (dashboardFormConfig) {
    // Adapter la structure de SmartForm à celle attendue par PublicSmartFormPage (FormConfig)
    // Ce mapping est approximatif et pourrait nécessiter des ajustements
    const publicConfig = {
      formId: dashboardFormConfig.id,
      title: dashboardFormConfig.name,
      description: dashboardFormConfig.description || '',
      fields: dashboardFormConfig.fields.map(field => ({
        id: field.id,
        name: field.name, // ou field.id si c'est ce que le formulaire public attend pour `name`
        label: field.name,
        type: field.type as any, // Attention au mapping des types
        required: field.required,
        options: field.options,
        // value: undefined, // Les valeurs pré-remplies pourraient venir d'ailleurs
        // readOnly: undefined, 
        placeholder: field.description, // ou un vrai placeholder
      })),
      // deadline: undefined, // à ajouter si pertinent
    };
    return NextResponse.json(publicConfig);
  }

  // Fallback sur mockFormConfigs pour les formulaires qui ne sont pas dans le dashboard (ex: démos pures)
  if (formId && mockFormConfigs[formId]) {
    return NextResponse.json(mockFormConfigs[formId]);
  }
  return new NextResponse('Configuration de formulaire non trouvée', { status: 404 });
}

// Implémentation de la route POST pour la soumission de formulaire
export async function POST(
  request: Request,
  { params }: { params: { formId: string } }
) {
  const formId = params.formId;
  console.log(`[API POST /api/smart-forms/${formId}] Tentative de soumission.`); // LOG 1: Début de la requête POST
  
  try {
    const submissionData = await request.json();
    console.log(`[API POST /api/smart-forms/${formId}] Données reçues:`, submissionData); // LOG 2: Données de soumission

    // LOG 3: Afficher les IDs disponibles dans mockSmartForms
    const availableFormIds = mockSmartForms.map(f => f.id);
    console.log(`[API POST /api/smart-forms/${formId}] IDs disponibles dans mockSmartForms:`, availableFormIds);
    console.log(`[API POST /api/smart-forms/${formId}] ID recherché:`, formId);

    // Trouver l'index du formulaire dans mockSmartForms
    const formIndex = mockSmartForms.findIndex(form => form.id === formId);
    console.log(`[API POST /api/smart-forms/${formId}] formIndex trouvé:`, formIndex); // LOG 4: Résultat de la recherche

    if (formIndex === -1 && !formId.startsWith('custom-')) { // Si pas trouvé et pas un custom
        // Pour les formulaires qui commencent par "form_", vérifier d'abord s'ils ont été créés via l'API
        // et ajoutés à mockSmartForms avec un ID différent (sf-XXXXX)
        if (formId.startsWith('form_')) {
            console.log(`[API POST /api/smart-forms/${formId}] Formulaire wizard non trouvé dans mockSmartForms. Traitement simulé.`);
            await new Promise(resolve => setTimeout(resolve, 500));
            const simulatedId = `wizard-submission-${Date.now()}`;
            return NextResponse.json({
              message: 'Données du formulaire wizard reçues et traitées avec succès!',
              formId: formId,
              submissionId: simulatedId,
              receivedData: submissionData
            });
        }

        // Fallback: vérifier dans mockFormConfigs si ce n'est pas un formulaire géré par mockSmartForms
        console.log(`[API POST /api/smart-forms/${formId}] Formulaire non trouvé dans mockSmartForms. Vérification dans mockFormConfigs.`); // LOG 5
        if (!mockFormConfigs[formId]) {
            console.error(`[API POST /api/smart-forms/${formId}] Formulaire ${formId} non trouvé ni dans mockSmartForms ni dans mockFormConfigs.`); // LOG 6
            return new NextResponse('Formulaire cible non trouvé pour la soumission', { status: 404 });
        }
        // Si c'est un formId de mockFormConfigs mais pas dans mockSmartForms,
        // on ne peut pas le mettre à jour pour la démo du dashboard.
        // On simule juste la sauvegarde comme avant pour ces cas.
        console.warn(`Formulaire ${formId} trouvé dans mockFormConfigs mais pas dans mockSmartForms. Sauvegarde simulée uniquement.`);
        await new Promise(resolve => setTimeout(resolve, 500));
        const simulatedId = `simulated-submission-${Date.now()}`;
        return NextResponse.json({
          message: 'Données du formulaire reçues (simulation pour config non-dashboard)!',
          formId: formId,
          submissionId: simulatedId,
          receivedData: submissionData
        });
    }

    // Si c'est un formulaire "custom-", on ne le met pas à jour dans mockSmartForms car il n'y est pas.
    if (formId.startsWith('custom-')) {
        console.log(`Soumission pour formulaire custom ${formId}. Traitement simulé.`);
        await new Promise(resolve => setTimeout(resolve, 500));
        const simulatedId = `simulated-submission-${Date.now()}`;
        return NextResponse.json({ 
          message: 'Données du formulaire custom reçues et traitées (simulation)!', 
          formId: formId, 
          submissionId: simulatedId,
          receivedData: submissionData 
        });
    }

    // Si on a trouvé le formulaire dans mockSmartForms
    if (formIndex !== -1) {
      const submissionId = `response-${Date.now()}`;
      const newResponse: SmartFormResponse = {
        id: submissionId,
        formId: formId,
        status: 'completed', // Marquer la réponse comme complétée
        submittedAt: new Date(),
        data: submissionData,
        // Simuler clientInfo si nécessaire, ou extraire de submissionData si disponible
        clientInfo: {
          email: submissionData.email_contact || submissionData.email || 'N/A', // Tenter de trouver un email
          name: submissionData.nom_prenom || submissionData.nom_complet || 'Utilisateur Anonyme' // Tenter de trouver un nom
        }
      };

      // S'assurer que le tableau responses existe
      if (!mockSmartForms[formIndex].responses) {
        mockSmartForms[formIndex].responses = [];
      }
      mockSmartForms[formIndex].responses!.push(newResponse);
      
      // Optionnel: Mettre à jour le statut du formulaire global
      mockSmartForms[formIndex].status = 'completed'; 
      mockSmartForms[formIndex].updatedAt = new Date();

      console.log(`[API POST /api/smart-forms/${formId}] Soumission ${submissionId} ajoutée à mockSmartForms.`); // LOG 7
      console.log('[API POST /api/smart-forms/${formId}] mockSmartForms après mise à jour:', JSON.stringify(mockSmartForms[formIndex], null, 2)); // LOG 8 (plus détaillé)

      // Diffuser un événement SSE pour notifier les clients connectés
      broadcastEvent('form-response-added', {
        formId: formId,
        submissionId: submissionId,
        responseCount: mockSmartForms[formIndex].responses?.length || 0,
        timestamp: new Date().toISOString()
      });

      return NextResponse.json({
        message: 'Données du formulaire reçues et mockSmartForms mis à jour!',
        formId: formId,
        submissionId: submissionId,
        receivedData: submissionData
      });
    }
    
    // Ce point ne devrait pas être atteint si la logique ci-dessus est correcte
    return new NextResponse('Erreur inattendue lors du traitement de la soumission', { status: 500 });

  } catch (error) {
    console.error(`[API POST /api/smart-forms/${formId}] Erreur lors de la soumission:`, error); // LOG 9 (plus détaillé)
    return new NextResponse('Erreur lors du traitement des données du formulaire', { status: 500 });
  }
} 