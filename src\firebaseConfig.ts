import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, signInWithPopup, GoogleAuthProvider, signOut, User, onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword } from "firebase/auth";
import { AuthError, getFirebaseErrorMessage } from "@/types/errors";

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialisation de Firebase
const app = initializeApp(firebaseConfig);
const analytics = typeof window !== "undefined" ? getAnalytics(app) : undefined;

// Initialisation de l'auth
const auth = getAuth(app);

// Connexion avec Google
export const signInWithGoogle = async () => {
  try {
    const provider = new GoogleAuthProvider();
    return await signInWithPopup(auth, provider);
  } catch (error: any) {
    if (error?.code) {
      throw new AuthError(getFirebaseErrorMessage(error.code), error.code);
    }
    throw new AuthError('Erreur lors de la connexion avec Google', 'unknown');
  }
};

// Connexion avec email/mot de passe
export const signInWithEmail = async (email: string, password: string) => {
  try {
    return await signInWithEmailAndPassword(auth, email, password);
  } catch (error: any) {
    if (error?.code) {
      throw new AuthError(getFirebaseErrorMessage(error.code), error.code);
    }
    throw new AuthError('Erreur lors de la connexion', 'unknown');
  }
};

// Création de compte avec email/mot de passe
export const signUpWithEmail = async (email: string, password: string) => {
  try {
    return await createUserWithEmailAndPassword(auth, email, password);
  } catch (error: any) {
    if (error?.code) {
      throw new AuthError(getFirebaseErrorMessage(error.code), error.code);
    }
    throw new AuthError('Erreur lors de la création du compte', 'unknown');
  }
};

// Déconnexion
export const logout = () => signOut(auth);

// Observer l'état de connexion
export const observeUser = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

export { app, analytics, auth }; 