import { NextRequest } from 'next/server';

// Store pour les connexions SSE actives
const connections = new Set<ReadableStreamDefaultController>();

// Fonction pour envoyer un événement à toutes les connexions
export function broadcastEvent(event: string, data: any) {
  const message = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
  
  connections.forEach(controller => {
    try {
      controller.enqueue(new TextEncoder().encode(message));
    } catch (error) {
      // Connexion fermée, la supprimer
      connections.delete(controller);
    }
  });
  
  console.log(`[SSE] Événement diffusé: ${event}`, { connectionsCount: connections.size, data });
}

export async function GET(request: NextRequest) {
  console.log('[SSE] Nouvelle connexion SSE établie');
  
  const stream = new ReadableStream({
    start(controller) {
      // Ajouter cette connexion à la liste
      connections.add(controller);
      
      // Envoyer un message de connexion
      const welcomeMessage = `event: connected\ndata: ${JSON.stringify({ message: 'Connexion SSE établie' })}\n\n`;
      controller.enqueue(new TextEncoder().encode(welcomeMessage));
      
      // Heartbeat pour maintenir la connexion
      const heartbeat = setInterval(() => {
        try {
          const heartbeatMessage = `event: heartbeat\ndata: ${JSON.stringify({ timestamp: Date.now() })}\n\n`;
          controller.enqueue(new TextEncoder().encode(heartbeatMessage));
        } catch (error) {
          clearInterval(heartbeat);
          connections.delete(controller);
        }
      }, 30000); // Heartbeat toutes les 30 secondes
      
      // Nettoyer quand la connexion se ferme
      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat);
        connections.delete(controller);
        console.log('[SSE] Connexion SSE fermée');
      });
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}
