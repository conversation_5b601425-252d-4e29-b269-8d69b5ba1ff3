"use client";

import { useEffect, useState } from "react";
import AuthButtons from "@/components/AuthButtons";
import { observeUser, logout } from "../firebaseConfig";
import type { User } from "firebase/auth";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [hover, setHover] = useState(false);

  // Rediriger automatiquement vers le dashboard si l'utilisateur est connecté
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-blue-50 flex flex-col font-sans">
      {/* Header */}
      <header className="flex justify-between items-center px-8 py-6 border-b border-gray-100 shadow-sm bg-white/80 backdrop-blur-md">
        <div className="flex items-center gap-3">
          {/* Logo vectoriel simple */}
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="20" fill="#2563eb" />
            <path d="M13 27L20 13L27 27" stroke="#fff" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
            <circle cx="20" cy="24" r="2" fill="#fff" />
          </svg>
          <span className="text-2xl font-bold tracking-tight">Diktasis</span>
        </div>
        <div>
          {user ? (
            <button
              onClick={() => router.push('/dashboard')}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-indigo-50 hover:bg-indigo-100 text-indigo-700 font-semibold border border-indigo-200 transition cursor-pointer"
            >
              <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
              Mon Compte
            </button>
          ) : (
            <button
              onClick={() => router.push('/login')}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-indigo-600 hover:bg-indigo-700 text-white font-semibold transition cursor-pointer"
            >
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
              Sign In
            </button>
          )}
        </div>
      </header>

      {/* Hero section */}
      <section className="flex-1 flex flex-col items-center justify-center px-4 py-16">
        <div className="max-w-2xl w-full text-center">
          <h1 className="text-5xl sm:text-6xl font-extrabold mb-4 bg-gradient-to-r from-blue-600 to-blue-400 text-transparent bg-clip-text">Partage sécurisé, conformité garantie</h1>
          <p className="text-lg text-gray-700 mb-8">
            Diktasis est la plateforme moderne pour partager, organiser et structurer vos informations sensibles, en toute sécurité et conformité RGPD. Collaborez efficacement, gardez le contrôle sur vos données et respectez la vie privée de vos utilisateurs.
          </p>
        </div>

        {/* Section principale des fonctionnalités */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl w-full mt-12">
          <div className="bg-white rounded-xl p-6 shadow-lg flex flex-col items-center border border-blue-100">
            <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><rect x="6" y="8" width="20" height="16" rx="3" fill="#2563eb"/><rect x="10" y="12" width="12" height="2" rx="1" fill="#fff"/><rect x="10" y="16" width="8" height="2" rx="1" fill="#fff"/></svg>
            <h2 className="font-semibold text-lg mt-4 mb-2">Structuration intelligente</h2>
            <p className="text-gray-500 text-center text-sm">Organisez et structurez vos données pour un accès rapide, clair et maîtrisé.</p>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg flex flex-col items-center border border-blue-100">
            <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><circle cx="16" cy="16" r="12" fill="#2563eb"/><path d="M16 10v6l4 2" stroke="#fff" strokeWidth="2" strokeLinecap="round"/></svg>
            <h2 className="font-semibold text-lg mt-4 mb-2">Partage sécurisé</h2>
            <p className="text-gray-500 text-center text-sm">Partagez vos informations avec les bonnes personnes, en toute sécurité et confidentialité.</p>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg flex flex-col items-center border border-blue-100">
            <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><rect x="8" y="8" width="16" height="16" rx="4" fill="#2563eb"/><path d="M12 16l4 4 4-4" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
            <h2 className="font-semibold text-lg mt-4 mb-2">Conformité RGPD</h2>
            <p className="text-gray-500 text-center text-sm">Respect total de la réglementation sur la protection des données personnelles (RGPD).</p>
          </div>
        </div>

        {/* Section détaillée des fonctionnalités */}
        <div className="max-w-6xl w-full mt-24 px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Intégration CRM */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Intégration CRM Unifiée</h3>
                  <p className="text-gray-600">Connectez-vous instantanément à tous vos CRM (Salesforce, Hubspot, Odoo) via une API unifiée. Gestion sécurisée des accès et synchronisation en temps réel.</p>
                </div>
              </div>
            </div>

            {/* Automatisation */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#2563eb" strokeWidth="2" strokeLinecap="round"/></svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Automatisation Intelligente</h3>
                  <p className="text-gray-600">Enrichissement automatique des données via des sources fiables (Whois, Clearbit, Hunter). Pré-remplissage intelligent des formulaires et validation en temps réel.</p>
                </div>
              </div>
            </div>

            {/* Vue 360° */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Vue 360° des Clients</h3>
                  <p className="text-gray-600">Visualisez toutes les informations client en un coup d'œil. Tableaux de bord personnalisables, métriques clés et historique des interactions.</p>
                </div>
              </div>
            </div>

            {/* Sécurité */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24"><path d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Sécurité Maximale</h3>
                  <p className="text-gray-600">Protection avancée des données avec chiffrement, authentification multi-facteurs et audit complet des accès. Conformité RGPD garantie.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section IA */}
          <div className="mt-24 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Intelligence Artificielle Intégrée</h2>
              <p className="text-gray-600 mb-8">Notre plateforme utilise des agents IA contextuels pour vous assister dans la gestion de vos données et l'enrichissement de vos informations clients.</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="font-semibold mb-2">Assistant d'Onboarding</h3>
                  <p className="text-sm text-gray-500">Guide intelligent pour la configuration et l'intégration de vos systèmes.</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="font-semibold mb-2">Enrichissement Automatique</h3>
                  <p className="text-sm text-gray-500">Analyse et complétion automatique des données manquantes.</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="font-semibold mb-2">Suggestions Intelligentes</h3>
                  <p className="text-sm text-gray-500">Recommandations personnalisées basées sur vos données.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="text-center text-xs text-gray-400 py-4 border-t border-gray-100">
        &copy; {new Date().getFullYear()} Diktasis. Tous droits réservés.
      </footer>
    </div>
  );
}
