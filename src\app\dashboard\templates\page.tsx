'use client';
import React, { useState } from 'react';
import { Box, Flex, Card, Heading, Text, Button, Grid, Badge, TextField, IconButton } from '@radix-ui/themes';
import { MagnifyingGlassIcon, PlusIcon, TrashIcon, Pencil1Icon, MagicWandIcon } from '@radix-ui/react-icons';

// Mock des modèles de champs
const mockTemplates = [
  {
    id: 'billing-template',
    name: 'Coordonnées de facturation',
    description: 'Collecte les informations légales et d\'adressage nécessaires à la facturation',
    icon: '🧾',
    fieldsCount: 8,
    usageCount: 15,
    lastUsed: '2024-12-18',
    tags: ['Facturation', 'Légal', 'Adresse']
  },
  {
    id: 'contact-template',
    name: 'Informations contact principal',
    description: 'Collecte les coordonnées complètes du contact principal de l\'entreprise',
    icon: '👤',
    fieldsCount: 6,
    usageCount: 23,
    lastUsed: '2024-12-19',
    tags: ['Contact', 'Communication']
  },
  {
    id: 'sales-template',
    name: 'Qualification commerciale',
    description: 'Collecte les informations de qualification pour le processus de vente',
    icon: '💼',
    fieldsCount: 12,
    usageCount: 8,
    lastUsed: '2024-12-17',
    tags: ['Commercial', 'Qualification', 'Budget']
  },
  {
    id: 'tech-template',
    name: 'Informations techniques',
    description: 'Collecte les détails techniques et d\'intégration nécessaires au projet',
    icon: '⚙️',
    fieldsCount: 7,
    usageCount: 5,
    lastUsed: '2024-12-16',
    tags: ['Technique', 'Intégration', 'Système']
  },
  {
    id: 'complete-template',
    name: 'Enrichissement complet',
    description: 'Sélectionne tous les champs disponibles pour un enrichissement maximal',
    icon: '🚀',
    fieldsCount: 35,
    usageCount: 3,
    lastUsed: '2024-12-15',
    tags: ['Complet', 'Toutes sources']
  }
];

export default function TemplatesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [templates, setTemplates] = useState(mockTemplates);

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Box style={{ maxWidth: 'var(--layout-max-width, 1280px)', margin: '0 auto' }} p={{ initial: '3', md: '6' }}>
      {/* Header de la page */}
      <Flex direction={{ initial: 'column', md: 'row' }} justify="between" align={{ initial: 'start', md: 'center' }} mb="6" gap="4">
        <Box>
          <Heading as="h1" size="8" weight="bold" mb="1">
            Mes Modèles
          </Heading>
          <Text color="gray" size="4">
            Gérez vos modèles de champs prédéfinis pour vos SmartForms.
          </Text>
        </Box>
        <Button size="3">
          <PlusIcon width="16" height="16" />
          Créer un modèle
        </Button>
      </Flex>

      {/* Barre de recherche */}
      <Box mb="6">
        <TextField.Root
          placeholder="Rechercher dans vos modèles..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="3"
          style={{ maxWidth: '400px' }}
        >
          <TextField.Slot>
            <MagnifyingGlassIcon height="16" width="16" />
          </TextField.Slot>
        </TextField.Root>
      </Box>

      {/* Liste des modèles */}
      {filteredTemplates.length === 0 ? (
        <Card variant="surface" style={{ borderStyle: 'dashed', textAlign: 'center', padding: 'var(--space-8)' }}>
          <MagicWandIcon width="48" height="48" color="var(--gray-9)" style={{ margin: '0 auto 16px' }} />
          <Heading as="h3" size="4" weight="medium" color="gray" mb="2">
            {searchTerm ? 'Aucun modèle trouvé' : 'Aucun modèle créé'}
          </Heading>
          <Text size="3" color="gray" mb="4">
            {searchTerm 
              ? `Aucun modèle ne correspond à "${searchTerm}"`
              : 'Créez votre premier modèle pour gagner du temps lors de la création de SmartForms.'
            }
          </Text>
          {!searchTerm && (
            <Button size="3">
              <PlusIcon width="16" height="16" />
              Créer mon premier modèle
            </Button>
          )}
        </Card>
      ) : (
        <Grid columns={{ initial: '1', sm: '2', lg: '3' }} gap="5">
          {filteredTemplates.map((template) => (
            <Card key={template.id} variant="surface" size="3" style={{ display: 'flex', flexDirection: 'column' }}>
              {/* En-tête du modèle */}
              <Flex align="start" gap="3" mb="3">
                <Box
                  style={{
                    fontSize: '32px',
                    minWidth: '48px',
                    textAlign: 'center'
                  }}
                >
                  {template.icon}
                </Box>
                <Box style={{ flexGrow: 1 }}>
                  <Heading as="h3" size="4" weight="medium" mb="1">
                    {template.name}
                  </Heading>
                  <Text size="2" color="gray" style={{ lineHeight: '1.4' }}>
                    {template.description}
                  </Text>
                </Box>
                <Flex gap="1">
                  <IconButton variant="ghost" size="1" color="gray">
                    <Pencil1Icon width="14" height="14" />
                  </IconButton>
                  <IconButton variant="ghost" size="1" color="red">
                    <TrashIcon width="14" height="14" />
                  </IconButton>
                </Flex>
              </Flex>

              {/* Tags */}
              <Flex gap="1" mb="3" wrap="wrap">
                {template.tags.map((tag) => (
                  <Badge key={tag} variant="soft" color="blue" size="1">
                    {tag}
                  </Badge>
                ))}
              </Flex>

              {/* Statistiques */}
              <Box style={{ marginTop: 'auto' }}>
                <Flex justify="between" align="center" mb="2">
                  <Text size="1" color="gray">
                    {template.fieldsCount} champs
                  </Text>
                  <Text size="1" color="gray">
                    Utilisé {template.usageCount} fois
                  </Text>
                </Flex>
                <Text size="1" color="gray">
                  Dernière utilisation : {new Date(template.lastUsed).toLocaleDateString('fr-FR')}
                </Text>
              </Box>

              {/* Actions */}
              <Flex gap="2" mt="4">
                <Button variant="soft" size="2" style={{ flex: 1 }}>
                  Utiliser ce modèle
                </Button>
                <Button variant="outline" size="2" style={{ flex: 1 }}>
                  Dupliquer
                </Button>
              </Flex>
            </Card>
          ))}
        </Grid>
      )}

      {/* Section informative */}
      <Box mt="8" p="6" style={{ 
        backgroundColor: 'var(--blue-2)', 
        borderRadius: 'var(--radius-4)',
        border: '1px solid var(--blue-6)'
      }}>
        <Flex align="start" gap="3">
          <MagicWandIcon width="24" height="24" color="var(--blue-9)" style={{ marginTop: '2px' }} />
          <Box>
            <Heading as="h3" size="4" weight="medium" color="blue" mb="2">
              À propos des modèles
            </Heading>
            <Text size="3" color="blue" style={{ lineHeight: '1.6' }}>
              Les modèles vous permettent de prédéfinir des ensembles de champs pour vos cas d'usage récurrents. 
              Ils accélèrent la création de SmartForms en sélectionnant automatiquement les champs pertinents 
              selon le contexte (facturation, prospection, qualification, etc.).
            </Text>
          </Box>
        </Flex>
      </Box>
    </Box>
  );
} 