import React from 'react';
import { Box, Card, Flex, Link, Text } from '@radix-ui/themes';
import { InfoCircledIcon } from '@radix-ui/react-icons';

export const DemoDataExplainer: React.FC = () => {
  return (
    <Card size="2" variant="surface" style={{ 
      borderLeft: '4px solid var(--amber-9)',
      backgroundColor: 'var(--amber-2)'
    }}>
      <Flex align="center" gap="3">
        <Box style={{ 
          background: 'var(--amber-3)', 
          borderRadius: 'var(--radius-2)', 
          padding: 'var(--space-2)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <InfoCircledIcon color="var(--amber-11)" width={16} height={16} />
        </Box>
        <Box style={{ flexGrow: 1 }}>
          <Text weight="bold" size="2" color="amber" mb="2" as="div">
            Données de démonstration
          </Text>
          <Text size="2" color="gray" as="div">
            Ces données illustrent le fonctionnement de Diktasis. {' '}
            <Link href="/dashboard/data-sources" color="blue" weight="medium">
              Connectez vos vraies sources
            </Link>
            {' '} pour commencer à utiliser vos propres données.
          </Text>
        </Box>
      </Flex>
    </Card>
  );
}; 