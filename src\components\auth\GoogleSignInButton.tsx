"use client";
import { useState } from "react";
import { But<PERSON> } from "@radix-ui/themes";
import { signInWithGoogle } from "../../firebaseConfig";
import { isAuthError, getErrorMessage } from "@/types/errors";
import { authLogger } from "@/utils/logger";

interface GoogleSignInButtonProps {
  onError: (error: string) => void;
  disabled?: boolean;
}

export const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({ 
  onError, 
  disabled = false 
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    authLogger.info('Attempting Google sign in');
    
    try {
      await signInWithGoogle();
      authLogger.info('Google sign in successful');
    } catch (error) {
      authLogger.error('Google sign in failed', error);
      if (isAuthError(error)) {
        onError(error.message);
      } else {
        onError(getErrorMessage(error));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleGoogleSignIn}
      disabled={disabled || isLoading}
      loading={isLoading}
      size="3"
      style={{ width: '100%' }}
    >
      {!isLoading && '🔗 '} Se connecter avec Google
    </Button>
  );
};
