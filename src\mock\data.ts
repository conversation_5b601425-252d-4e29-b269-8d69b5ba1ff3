import { OdooField, SmartForm, SmartFormResponse, DashboardStats, UserProfile, OdooFieldOption } from '../types/dashboard';
import { logger } from '../utils/logger';

// Déclaration pour TypeScript afin qu'il connaisse la propriété sur l'objet global
declare global {
  var __mockSmartForms: SmartForm[] | undefined;
}

export const mockOdooFields: OdooField[] = [
  {
    id: 'company_name',
    name: 'Dénomination sociale',
    type: 'string',
    required: true,
    category: 'identity',
    description: 'Nom légal de l\'entreprise'
  },
  {
    id: 'vat_number',
    name: 'Numéro de TVA',
    type: 'string',
    required: true,
    category: 'financial',
    description: 'Numéro de TVA intracommunautaire'
  },
  {
    id: 'legal_form',
    name: 'Forme juridique',
    type: 'select',
    required: true,
    category: 'identity',
    options: [
      { id: 'sarl', name: 'SARL' },
      { id: 'sas', name: '<PERSON>' },
      { id: 'sa', name: '<PERSON>' },
      { id: 'ei', name: 'E<PERSON>' },
      { id: 'ae', name: 'Auto-entrepreneur' }
    ],
    description: 'Forme juridique de l\'entreprise'
  },
  {
    id: 'address',
    name: 'Adresse complète',
    type: 'string',
    required: true,
    category: 'contact',
    description: 'Adresse postale complète'
  },
  {
    id: 'email',
    name: 'Email de facturation',
    type: 'string',
    required: true,
    category: 'financial',
    description: 'Email pour l\'envoi des factures'
  },
  {
    id: 'phone',
    name: 'Téléphone',
    type: 'string',
    required: true,
    category: 'contact',
    description: 'Numéro de téléphone principal'
  }
];

// Logique pour initialiser mockSmartForms une seule fois
if (!global.__mockSmartForms) {
  logger.debug('Initializing __mockSmartForms on global object', 'MockData');
  global.__mockSmartForms = [
    {
      id: '1',
      name: "Formulaire d'ouverture de dossier",
      description: "Formulaire standard pour l'ouverture d'un nouveau dossier client",
      fields: mockOdooFields,
      status: 'sent',
      createdAt: new Date('2024-03-15'),
      updatedAt: new Date('2024-03-15'),
      template: true,
      responses: []
    },
    {
      id: '2',
      name: "Demande d'informations - Client XYZ",
      description: "Demande d'informations complémentaires pour le client XYZ",
      fields: mockOdooFields.filter(f => ['company_name', 'vat_number', 'email'].includes(f.id)),
      status: 'sent',
      createdAt: new Date('2024-03-14'),
      updatedAt: new Date('2024-03-14'),
      template: false,
      responses: [
        {
          id: 'resp1',
          formId: '2',
          status: 'completed',
          submittedAt: new Date('2024-03-14T15:30:00'),
          data: {
            company_name: 'XYZ SARL',
            vat_number: 'FR12345678900',
            email: '<EMAIL>'
          },
          clientInfo: {
            email: '<EMAIL>',
            name: 'Jean Dupont'
          }
        }
      ]
    },
    {
      id: '3',
      name: 'Mise à jour données - TechCorp',
      description: 'Mise à jour des informations de contact et facturation',
      fields: mockOdooFields.filter(f => ['company_name', 'email', 'phone', 'address'].includes(f.id)),
      status: 'sent',
      createdAt: new Date('2024-03-13'),
      updatedAt: new Date('2024-03-13'),
      template: false,
      responses: []
    },
    {
      id: '4',
      name: 'Onboarding - StartupABC',
      description: "Collecte complète d'informations pour nouveau client",
      fields: mockOdooFields,
      status: 'sent',
      createdAt: new Date('2024-03-12'),
      updatedAt: new Date('2024-03-12'),
      template: false,
      responses: [
        {
          id: 'resp2',
          formId: '4',
          status: 'pending',
          submittedAt: undefined,
          data: {},
          clientInfo: {
            email: '<EMAIL>',
            name: 'Marie Martin'
          }
        }
      ]
    },
    {
      id: '5',
      name: 'Vérification fiscale - GlobalCorp',
      description: 'Vérification des informations fiscales et comptables',
      fields: mockOdooFields.filter(f => ['vat_number', 'legal_form', 'company_name'].includes(f.id)),
      status: 'submitted',
      createdAt: new Date('2024-03-11'),
      updatedAt: new Date('2024-03-11'),
      template: true,
      responses: [
        {
          id: 'resp3',
          formId: '5',
          status: 'completed',
          submittedAt: new Date('2024-03-12T09:15:00'),
          data: {
            company_name: 'GlobalCorp SAS',
            vat_number: 'FR98765432100',
            legal_form: 'SAS'
          },
          clientInfo: {
            email: '<EMAIL>',
            name: 'Pierre Durand'
          }
        }
      ]
    },
    {
      id: '6',
      name: 'Données bancaires - LocalBiz',
      description: 'Collecte des informations bancaires pour facturation',
      fields: mockOdooFields.filter(f => ['company_name', 'email', 'address'].includes(f.id)),
      status: 'submitted',
      createdAt: new Date('2024-03-10'),
      updatedAt: new Date('2024-03-10'),
      template: false,
      responses: [
        {
          id: 'resp4',
          formId: '6',
          status: 'completed',
          submittedAt: new Date('2024-03-10T14:45:00'),
          data: {
            company_name: 'LocalBiz SARL',
            email: '<EMAIL>',
            address: '123 Rue de la Paix, 75001 Paris'
          },
          clientInfo: {
            email: '<EMAIL>',
            name: 'Sophie Leblanc'
          }
        }
      ]
    }
  ];
} else {
  logger.debug('Reusing existing __mockSmartForms from global object', 'MockData');
}

export let mockSmartForms: SmartForm[] = global.__mockSmartForms!;

export const mockDashboardStats: DashboardStats = {
  totalForms: 12,
  activeForms: 5,
  completedForms: 7,
  responseRate: 85,
  averageResponseTime: 2.5 // en jours
};

export const mockUserProfile: UserProfile = {
  id: '1',
  name: 'Justine Martin',
  email: '<EMAIL>',
  role: 'admin',
  agency: {
    id: '1',
    name: 'Agence Immobilière Martin'
  },
  odooConnected: true,
  lastSync: new Date('2024-03-15T10:00:00')
}; 