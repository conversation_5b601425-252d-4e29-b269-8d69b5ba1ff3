# Architecture du Projet

## Vue d'ensemble

Ce projet vise à créer une plateforme de collecte et de gestion de données client, avec un focus sur l'automatisation et l'intégration avec les systèmes CRM existants.

## Stack Technique

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **UI**: 
  - **Radix UI Themes**: Bibliothèque principale de composants UI. Privilégier l'utilisation de ses composants (Box, Flex, Text, Button, Card, etc.) et de son système de theming (tokens de design pour les couleurs, espacements, radius) pour assurer la cohérence, l'accessibilité et faciliter la maintenance.
  - **Tailwind CSS**: Utilisé pour des ajustements de style fins ou des mises en page complexes non directement gérables par les props des composants Radix. L'objectif est de minimiser les conflits en priorisant les styles Radix.
  - Shadcn/ui: (À évaluer si toujours pertinent ou si Radix Themes couvre les besoins)
- **Composants spécialisés**:
  - **react-day-picker**: Composant de calendrier pour la sélection de dates avec localisation française
  - **date-fns**: Bibliothèque de manipulation et formatage des dates, utilisée avec react-day-picker
- **Gestion d'état**: React Query
- **Authentification**: NextAuth.js

### Backend
- **API Routes**: Next.js (Phase 1)
- **Migration future**: NestJS (si nécessaire)

### Base de données
- **Principal**: PostgreSQL (Cloud SQL - GCP)
- **Cache**: Redis (Phase 2)

### Infrastructure
- **Cloud**: Google Cloud Platform (GCP)
  - Cloud Run
  - Cloud SQL
  - Cloud Storage
- **Orchestration**: N8N (Cloud version pour MVP)

### Intégrations
- **Plateforme d'Intégration de Données**: Apideck
  - Gestion des credentials via Vault
  - API unifiée pour de nombreuses applications (CRM majeurs comme Attio, Salesforce, HubSpot, tableurs collaboratifs comme Google Sheets ou Airtable, outils de productivité comme Notion, etc.)
  - SDK React disponible
- **Logos d'applications**: Utilisation de [Simple Icons](https://simpleicons.org/) via CDN pour l'affichage des logos des services externes (ex: sources de données). Permet une standardisation et évite le stockage local des SVGs.

## Plan de Développement MVP

### Phase 1 : Setup Initial (1-2 semaines)
- Configuration du projet Next.js
- Setup TypeScript et UI
- Configuration GCP
- Mise en place de l'authentification

### Phase 2 : Core Features (2-3 semaines)
- Intégration Apideck
- Formulaire de base
- Système de demande d'informations (SmartForm)

### Phase 3 : Automatisation (2-3 semaines)
- Scraping intelligent (Whois, Clearbit, Hunter)
- Système de pré-remplissage
- Configuration N8N

### Phase 4 : Dashboard & Monitoring (1-2 semaines)
- Vue 360° des clients
- Système de monitoring
- Logs et métriques
- **Panneau d'administration SaaS**: Gestion des clients (visualisation, plans, etc.), statistiques d'utilisation, consultation des logs, et outils de support.

### Phase 5 : Tests & Optimisation (1-2 semaines)
- Tests unitaires et d'intégration
- Optimisation performance
- Documentation

## Points d'Attention

### Sécurité
- Gestion sécurisée des tokens OAuth
- Pas de stockage permanent des données sensibles
- Validation des données

### Performance
- Mise en cache optimisée
- Requêtes optimisées
- Scalabilité horizontale

### UX
- Interface intuitive
- Validation en temps réel
- Feedback utilisateur

## Coûts Estimés (MVP)

- GCP: ~$50-100/mois
- N8N Cloud: ~$20/mois
- Vercel: Gratuit (démarrage)
- Total: ~$70-120/mois

## Prochaines Étapes Post-MVP

1. Agents IA
   - Onboarding Helper
   - Access Tracker
2. Extension Chrome
3. Intégrations supplémentaires
4. Fonctionnalités avancées

## Décisions Techniques

### Choix d'Apideck
- Simplifie l'intégration avec diverses sources de données (CRM, tableurs, etc.)
- Gestion sécurisée des credentials
- API unifiée
- SDK React disponible

### Choix de Next.js
- Performance optimale
- SEO intégré
- API Routes intégrées
- Facilité de déploiement

### Choix de GCP
- Intégration avec Google Workspace
- Facturation à l'usage
- Scalabilité automatique
- Services serverless 

## Intégration MCP (Model Context Protocol)

### Objectifs
- Améliorer l'assistance au développement
- Optimiser les interactions avec les agents IA
- Renforcer la sécurité et le suivi des accès
- **Assurer une extensibilité future**: Permettre à la plateforme d'interagir de manière flexible et standardisée avec un écosystème croissant d'applications et d'outils.
- **Positionnement technologique**: Maintenir le projet à la pointe de la technologie pour offrir une valeur ajoutée maximale et continue.

### Implémentation

#### Assistant de Développement
- Intégration avec Cursor
- Connexion à la base de code
- Accès à la documentation technique
- Support contextuel pour les intégrations

#### Agents IA
- Chatbot d'onboarding contextuel
- Génération de propositions intelligentes
- Analyse des données structurées
- Analyse des données inter-applications et suggestions intelligentes (ex: génération de description LinkedIn basée sur les services, le secteur et autres données de l'entreprise)

#### Access Tracker
- Analyse des niveaux d'accès
- Détection d'anomalies
- Recommandations de sécurité
- Audit automatisé

### Avantages
- Développement plus efficace
- Meilleure expérience utilisateur
- Sécurité renforcée
- Maintenance simplifiée 

## Implémentation Technique

### Serveurs MCP
- Serveur Apideck pour l'intégration des connecteurs de données
- Serveur personnalisé pour le scraping
- Configuration via settings.json

### Agents IA
- Structure TypeScript pour le contexte
- Interfaces pour les réponses
- Système de validation

### Système d'Accès
- Logs détaillés
- Politiques d'accès
- Monitoring en temps réel

Pour plus de détails, voir [MCP_IMPLEMENTATION.md](MCP_IMPLEMENTATION.md) 