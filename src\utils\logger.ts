// Système de logging centralisé
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: string;
  data?: any;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  private formatMessage(level: LogLevel, message: string, context?: string, data?: any): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      data
    };
  }

  private log(level: LogLevel, message: string, context?: string, data?: any) {
    const logEntry = this.formatMessage(level, message, context, data);
    
    if (this.isDevelopment) {
      const prefix = context ? `[${context}]` : '';
      const fullMessage = `${prefix} ${message}`;
      
      switch (level) {
        case 'debug':
          console.debug(fullMessage, data || '');
          break;
        case 'info':
          console.info(fullMessage, data || '');
          break;
        case 'warn':
          console.warn(fullMessage, data || '');
          break;
        case 'error':
          console.error(fullMessage, data || '');
          break;
      }
    }

    // En production, on pourrait envoyer les logs vers un service externe
    // comme Sentry, LogRocket, etc.
    if (!this.isDevelopment && level === 'error') {
      // TODO: Envoyer vers un service de monitoring
      // this.sendToMonitoring(logEntry);
    }
  }

  debug(message: string, context?: string, data?: any) {
    this.log('debug', message, context, data);
  }

  info(message: string, context?: string, data?: any) {
    this.log('info', message, context, data);
  }

  warn(message: string, context?: string, data?: any) {
    this.log('warn', message, context, data);
  }

  error(message: string, context?: string, data?: any) {
    this.log('error', message, context, data);
  }

  // Méthode spéciale pour logger les erreurs avec stack trace
  logError(error: Error, context?: string, additionalData?: any) {
    this.error(error.message, context, {
      stack: error.stack,
      name: error.name,
      ...additionalData
    });
  }
}

// Instance singleton
export const logger = new Logger();

// Helpers pour des contextes spécifiques
export const authLogger = {
  debug: (message: string, data?: any) => logger.debug(message, 'Auth', data),
  info: (message: string, data?: any) => logger.info(message, 'Auth', data),
  warn: (message: string, data?: any) => logger.warn(message, 'Auth', data),
  error: (message: string, data?: any) => logger.error(message, 'Auth', data),
};

export const apiLogger = {
  debug: (message: string, data?: any) => logger.debug(message, 'API', data),
  info: (message: string, data?: any) => logger.info(message, 'API', data),
  warn: (message: string, data?: any) => logger.warn(message, 'API', data),
  error: (message: string, data?: any) => logger.error(message, 'API', data),
};

export const formLogger = {
  debug: (message: string, data?: any) => logger.debug(message, 'Form', data),
  info: (message: string, data?: any) => logger.info(message, 'Form', data),
  warn: (message: string, data?: any) => logger.warn(message, 'Form', data),
  error: (message: string, data?: any) => logger.error(message, 'Form', data),
};
